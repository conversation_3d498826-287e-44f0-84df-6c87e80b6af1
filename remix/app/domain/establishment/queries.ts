import { kysely } from "~/misc/database.server";
import { sql } from "kysely";
import { notNull, stAsGeoJsonPoint } from "~/kysely/kysely-helpers";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";

export const searchOperatorQb = (search?: string | null) =>
  kysely
    .selectFrom("establishment")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .select([
      "establishment.id as establishment_id",
      "operator.name as operator_name",
      "spot.name as spot_name",
      "establishment.location_name as establishment_name",
      (ob) => sql<number>`(similarity(${ob.ref("operator.name")} || ' ' || ${ob.ref("spot.name")},${search}))`.as("score"),
    ])
    .limit(0)
    .$if(!!search, (eb) =>
      eb
        .orderBy((ob) => sql`(similarity(${ob.ref("operator.name")} || ' ' || ${ob.ref("spot.name")},${search}))`, "desc")
        .where((ob) => sql`(similarity(${ob.ref("operator.name")} || ' ' || ${ob.ref("spot.name")},${search}))`, ">", 0.05)
        .limit(20),
    );

export const simpleEstablishmentQb = kysely
  .selectFrom("establishment")
  .innerJoin("operator", "operator.id", "establishment.operator_id")
  .leftJoin("spot", "spot.id", "establishment.spot_id")
  .leftJoin("region", "region.id", "spot.region_id")
  .select((eb) => [
    "operator.name as name",
    "operator.name as operator_name",
    "establishment.address",
    "establishment.id as establishment_id",
    "establishment.locale",
    "establishment.location_name",
    "establishment.location_name as establishment_name",
    "spot.name as spot_name",
    "region.country_code",
    "establishment.default_currency",
    "region.timezone",
    eb.fn.coalesce(eb.ref("establishment.website"), eb.ref("operator.website")).as("website"),
    eb.fn.coalesce(eb.ref("establishment.whatsapp"), eb.ref("operator.whatsapp")).as("whatsapp"),
    eb.fn.coalesce(eb.ref("establishment.email"), eb.ref("operator.email")).as("email"),
    eb.fn.coalesce(eb.ref("establishment.telephone"), eb.ref("operator.telephone")).as("telephone"),
  ]);

export const establishmentQb = simpleEstablishmentQb
  .selectAll("operator")
  .selectAll("establishment")
  .select((eb) => [
    stAsGeoJsonPoint(eb.ref("establishment.geom")).as("geom"),
    jsonObjectFrom(fileTargetsQb(kysely, "operator_logo", eb.ref("operator.id")).limit(1)).as("logo_file"),
    jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
    jsonArrayFrom(
      eb
        .selectFrom("addon")
        .whereRef("addon.establishment_id", "=", "establishment.id")
        .selectAll("addon")
        .select((eb) => [
          notNull(
            jsonObjectFrom(
              eb.selectFrom("price").whereRef("price.id", "=", "addon.price_id").select(["price.amount", "price.currency_id"]),
            ),
          ).as("price"),
        ]),
    ).as("addons"),
    jsonArrayFrom(
      eb
        .selectFrom("establishment_tank")
        .whereRef("establishment_tank.establishment_id", "=", "establishment.id")
        .orderBy("establishment_tank.sort_order")
        .select(["establishment_tank.id", "establishment_tank.value", "establishment_tank.sort_order"])
    ).as("tanks"),
  ]);
