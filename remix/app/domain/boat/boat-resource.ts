import { memberQb } from "~/domain/member/member-queries.server";
import { v4 } from "uuid";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { getCacheKey } from "~/server/cache/cache.planning.server";
import { Args, bustCacheAfter } from "~/server/resource/resource-helpers.server";
import { format } from "date-fns";
import { coalesceTextArray, formatDate } from "~/kysely/kysely-helpers";
import { TripAssignment } from "~/kysely/db";
import { gasOptions, literOptions } from "~/domain/tank/tank-vars";
import { executeActionAndCreateAuditInput, SimpleEntityAction } from "~/domain/table-action/table-action";
import { getActivity } from "~/domain/activity/activity";
import { defaultSites } from "~/domain/trip/trip.server";

export const boatResource: Args<"boat"> = {
  authorize: async (args) => {
    const boat = await memberQb(args)
      .innerJoin("boat", "boat.establishment_id", "_member.establishment_id")
      .where("_member.admin", ">=", getAdminLevelIndex("write"))
      .select("boat.establishment_id")
      .where("boat.id", "=", args.id)
      .executeTakeFirst();
    if (boat) {
      bustCacheAfter(args, getCacheKey({ establishmentId: boat.establishment_id }));
    }
    return boat;
  },
  insert: (args) => args.data,
  update: (args) => {
    delete args.data.establishment_id;
    return args.data;
  },
  delete: () => true,
};

export const tripResource: Args<"trip"> = {
  authorize: (args) =>
    memberQb(args)
      .where("_member.admin", ">=", getAdminLevelIndex("write"))
      .innerJoin("trip", "trip.establishment_id", "_member.establishment_id")
      .where("trip.id", "=", args.id)
      .select("trip.id")
      .executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  onChanged: async (args) => {
    const establishmentId = args.diff.before?.establishment_id || args.diff.after?.establishment_id;
    const date = args.diff.before?.date || args.diff.after?.date;
    const month = date && format(date, "yyyy-MM");
    if (establishmentId) {
      bustCacheAfter(args, getCacheKey({ establishmentId, date, type: "planning-day" }));
      bustCacheAfter(args, getCacheKey({ establishmentId, month }));
    }
    return true;
  },
  delete: () => true,
};

export const tripAssignmentResource: Args<"trip_assignment"> = {
  authorize: async (args) => {
    const trip = await memberQb(args)
      .where("_member.admin", ">=", getAdminLevelIndex("write"))
      .innerJoin("trip", "trip.establishment_id", "_member.establishment_id")
      .innerJoin("trip_assignment", "trip_assignment.trip_id", "trip.id")
      .where("trip_assignment.id", "=", args.id)
      .select((eb) => ["trip.establishment_id", "trip.date", formatDate(eb.ref("trip.date"), "YYYY-MM").as("month")])
      .executeTakeFirst();

    if (!trip) return false;

    const establishmentId = trip.establishment_id;
    bustCacheAfter(args, getCacheKey({ establishmentId, date: trip.date }));
    bustCacheAfter(args, getCacheKey({ establishmentId, month: trip.month }));

    return true;
  },
  beforeMutate: async (args) => {
    if (args.operation === "update" && args.data?.trip_id === null) {
      return { id: args.id, operation: "delete" };
    }
    return { id: args.id || v4(), operation: args.operation, data: args.data };
  },
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
  onChanged: async (args) => {
    const diff = args.diff.diff;
    if (diff && ("participation_id" satisfies keyof TripAssignment) in diff) {
      args.after_mutations.insideTransaction.set("auto_assign_tanks" + args.id, async () => {
        const tripAssignment = await args.trx
          .selectFrom("trip_assignment")
          .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
          .innerJoin("participation", "participation.id", "trip_assignment.participation_id")
          .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
          .innerJoin("product", "product.id", "sale_item.product_id")
          .innerJoin("item", "item.id", "product.item_id")
          .select(eb => ["trip_assignment.id", "trip_assignment.participation_id", "item.activity_slug", coalesceTextArray(eb.ref("trip.sites"), defaultSites).as("sites")])
          .where("trip_assignment.id", "=", args.id)
          .executeTakeFirst();

        if (!tripAssignment) return;
        await args.trx.deleteFrom("tank_assignment").where("tank_assignment.trip_assignment_id", "=", args.id).execute();

        const sitesCount = tripAssignment.sites?.length || 0;
        if (!sitesCount) return;
        const activity = getActivity(tripAssignment.activity_slug);
        if (!activity.count_tanks) return;

        const entityAction: SimpleEntityAction<"tank_assignment"> = {
          operation: "insert",
          entity_id: v4(),
          entity_name: "tank_assignment",
          data: {
            trip_assignment_id: tripAssignment.id,
            liters: literOptions[0],
            gas: gasOptions[0],
            quantity: tripAssignment.sites?.length,
            sort_order: 0,
          },
        };

        const audit = await executeActionAndCreateAuditInput(args.trx, entityAction);
        const auditDiff = audit?.auditInput;
        if (!auditDiff) return;
        return [
          {
            entity_name: entityAction.entity_name,
            entity_id: entityAction.entity_id,
            action_name: entityAction.operation,
            data: auditDiff,
          },
        ];
      });
    }
    return true;
  },
};
