import { EstablishmentLayout } from "~/components/AllowedForEstablishment";
import React, { ReactNode, useState } from "react";
import { useAppContext } from "~/hooks/use-app-context";
import { gasOptions, literOptions } from "~/domain/tank/tank-vars";
import { unique } from "remeda";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GrDrag } from "react-icons/gr";
import { PlusIcon, TrashIcon } from "@heroicons/react/20/solid";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import { fName, joinKeys } from "~/misc/helpers";
import { uniqueTextArray } from "~/misc/postgres-helpers";

export { action } from "~/routes/_all._catch.resource";

interface Item {
  id: string;
  value: string;
}

const SimpleSortableContext = (props: { children: ReactNode; items: Item[]; onChange: (items: Item[]) => void }) => {
  const items = props.items;
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );
  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={(e) => {
        const { active, over } = e;
        if (!over) return;
        if (active.id === over.id) return;

        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        const newItems = arrayMove(items, oldIndex, newIndex);
        // Update sort_order based on new positions
        const updatedItems = newItems.map((item, index) => ({
          ...item,
          sort_order: index,
        }));

        props.onChange(updatedItems);
      }}
    >
      <SortableContext items={items}>{props.children}</SortableContext>
    </DndContext>
  );
};

const SimpleSortableItem = ({
  item,
  onValueChange,
  onDelete,
}: {
  item: { id: string; value: string };
  onValueChange: (value: string) => void;
  onDelete: () => void;
}) => {
  const sortable = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(sortable.transform),
    transition: sortable.transition,
    opacity: sortable.isDragging ? 0.5 : 1,
  };

  return (
    <div className={"flex flex-row gap-2"} ref={sortable.setNodeRef} style={style} {...sortable.attributes}>
      <div className="flex items-center justify-center ">
        <button {...sortable.listeners} className="touch-none cursor-move p-2 hover:bg-gray-100 rounded transition-colors" type="button">
          <GrDrag className="w-5 h-5 text-gray-500 hover:text-gray-700" />
        </button>
      </div>
      <div>
        <input className="input w-full" type="text" value={item.value} onChange={(e) => onValueChange(e.target.value)} />
      </div>
      <div className="text-right flex items-center justify-end">
        <button type="button" onClick={onDelete} className="btn">
          <TrashIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};

const SimpleSortableForm = (props: { initialItems: string[]; name: string }) => {
  const ctx = useAppContext();
  const initialItems = unique(props.initialItems.filter(Boolean));
  const [count, setCount] = useState(initialItems.length);
  const [items, setItems] = useState(initialItems.map((tank, index) => ({ id: index + "", value: tank })));
  const finalItems = items.map((tank) => tank.value).filter(Boolean);

  return (
    <ActionForm
      onCheckEqual={() => {
        return initialItems.toString() === finalItems.toString();
      }}
      className="w-fit"
    >
      <RInput table={"establishment"} field={"id"} value={ctx.establishment?.id} />
      <HiddenTypeInput name={props.name} value={"__empty_array__"} />
      {items.map((item, index) => (
        <input type={"hidden"} key={item.id} name={joinKeys(props.name, index)} value={item.value} />
      ))}
      <div className="space-y-3 w-fit">
        <SimpleSortableContext items={items} onChange={(items) => setItems(items)}>
          {items.map((item) => (
            <SimpleSortableItem
              key={item.id}
              item={item}
              onValueChange={(value) => {
                setItems(items.map((t) => (t.id === item.id ? { ...t, value } : t)));
              }}
              onDelete={() => {
                setItems(items.filter((t) => t.id !== item.id));
              }}
            />
          ))}
        </SimpleSortableContext>
        <div className="flex flex-row gap-2 justify-end px-3">
          <button
            type="button"
            className="link flex flex-row items-center"
            onClick={() => {
              const newCount = count + 1;
              setCount(newCount);
              setItems([...items, { id: newCount + "", value: "" }]);
            }}
          >
            <PlusIcon className="w-5 h-5" /> add
          </button>
        </div>
      </div>
      <SubmitButton className="btn btn-primary">Save</SubmitButton>
    </ActionForm>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const initialTanks = uniqueTextArray(ctx.establishment?.tanks, literOptions);
  const initialBlends = uniqueTextArray(ctx.establishment?.blends, gasOptions);

  return (
    <EstablishmentLayout title={<h2 className="text-xl font-semibold text-slate-800">Tanks and Blends Settings</h2>}>
      <p className="text-slate-500">Renaming a tank or blend will NOT rename already assigned tanks.<br/>This is just a list of default options, where the first items is automatically selected.</p>
      <div className="space-y-9 pt-3 w-fit">
        <div className="space-y-3">
          <h3 className="text-xl">Tanks</h3>
          <SimpleSortableForm initialItems={initialTanks} name={fName("establishment", "data.tanks")} />
        </div>
        <hr className="border-2"/>
        <div className=" space-y-3">
        <h3 className="text-xl">Blends</h3>
        <SimpleSortableForm initialItems={initialBlends} name={fName("establishment", "data.blends")} />
          </div>
      </div>
    </EstablishmentLayout>
  );
}
