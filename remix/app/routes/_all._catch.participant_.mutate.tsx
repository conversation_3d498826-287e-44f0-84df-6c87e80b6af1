import { Trans } from "@lingui/react/macro";
import { t } from "@lingui/core/macro";
import { ChevronLeftIcon, ChevronRightIcon, UserIcon } from "@heroicons/react/20/solid";
import { useLingui } from "@lingui/react";
import { useLoaderData } from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import React, { Fragment, Suspense, useId, useMemo } from "react";
import { flat } from "remeda";
import { ActionAlert } from "~/components/ActionAlert";
import { Checker } from "~/components/Checker";
import { RInput, RLabel, RSelect, SimpleRInput } from "~/components/ResourceInputs";
import { SearchField, SearchForm } from "~/components/SearchForm";
import { SubmitButton } from "~/components/base/Button";
import { AnimatingDiv } from "~/components/base/base";
import { ActionForm } from "~/components/form/BaseFrom";
import { HiddenTypeInput, OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { getDurationInDays } from "~/components/form/RangeInput";
import { ParamLink } from "~/components/meta/CustomComponents";
import { activities, getActivitySlug } from "~/domain/activity/activity";
import { saleItemWithProductQb } from "~/domain/activity/activity-queries";
import { formatDuration } from "~/domain/activity/activity.helpers";
import type { AddonUnit } from "~/domain/addon/addon";
import { addonUnits } from "~/domain/addon/addon";
import { bookingQb } from "~/domain/booking/booking-queries";
import { CallbackInput } from "~/domain/callback/callback.components";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { establishmentQb } from "~/domain/establishment/queries";
import { activeUserSessionSimple, memberIsAdminOrOwnerQb, memberQb } from "~/domain/member/member-queries.server";
import { ParticipantFields } from "~/domain/participant/ParticipantFields";
import { allowedParticipantsForMember, myRegisteredParticipants } from "~/domain/participant/participant-auth-queries.server";
import {
  createPersonSearchQb,
  participantQb,
  participatingParticipantIdQb,
  participationQb,
  participationWaiversQb,
} from "~/domain/participant/participant.queries.server";
import { meetingTypes, participantsNrArray } from "~/domain/planning/plannings-consts";
import { arrayFrom } from "~/domain/planning/plannings-helpers";
import { ProductItem } from "~/domain/product/ProductItem";
import {
  baseProductQb,
  divingCoursesJsonEb,
  divingLevelsJsonEb,
  divingLocationsJsonEb,
  pricesJsonEb,
} from "~/domain/product/product-queries.server";
import { MarkdocComp } from "~/domain/waiver/waiver-components";
import { useAppContext } from "~/hooks/use-app-context";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { at_infinity_value } from "~/kysely/db-static-vars";
import {
  arrayAgg,
  arrayAggDisinct,
  ascNullsLast,
  notNull,
  nowValue,
  nowValueAdDate,
  tstzToDate,
  unnestArray,
} from "~/kysely/kysely-helpers";
import { createPageOverwrites, diversdeskName } from "~/misc/consts";
import { kysely } from "~/misc/database.server";
import { fName, tableIdRef } from "~/misc/helpers";
import { paramsToRecord, toggleArray } from "~/misc/parsers/global-state-parsers";
import { _booking_detail, _participant_detail } from "~/misc/paths";
import { notFound } from "~/misc/responses";
import { getClientLocales } from "~/misc/utils";
import { dateFormat, defaultCurrency, defaultLangauge, defaultLocale } from "~/misc/vars";
import { formatMoney } from "~/utils/money";
import { getSessionSimple } from "~/utils/session.server";
import { baseMarkdocComps } from "~/domain/waiver/waiver-markdoc";
import { createMeta } from "~/misc/route-helpers";
import { twMerge } from "tailwind-merge";
import { WaiverType } from "~/domain/waiver/waiver-vars";
import { CDialog, DialogCloseButton } from "~/components/base/Dialog";
import { PolicyLink, TermsLink } from "~/components/shared";
import { RegisterEmailInput } from "~/domain/user/UserRegisterFields";
import { AuthzMethod } from "~/domain/user_session/user_session";
import { OtpBlock, OtpGoBackBlock } from "~/domain/otp/otp-components";
import { CheckDoneIcon } from "~/components/Icons";
import { DefaultInfoIcon, Tooltip } from "~/components/base/tooltip";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { useBoolean } from "~/hooks/use-boolean";
import { addDays, format } from "date-fns";

import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { plus, toDate } from "../kysely/kysely-helpers";

const itemPerPage = 9;

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const dateObj = getDateFromParams(request);

  const isCopy = state.action_type === "copy";

  const getLocals = () => {
    const clientLocals = getClientLocales(request) || [];
    return [state.lang, ...clientLocals, defaultLangauge].filter((locale) => !!locale).map((locale) => locale?.toLowerCase() as string);
  };
  const locales = getLocals();

  const qbArgs = { trx: kysely, ctx: ctx };

  const finalEstablishmentQb = establishmentQb.select((eb) => [
    toDate(plus(tstzToDate(nowValue, eb.ref("region.timezone")), eb.ref("establishment.direct_booking_window"))).as(
      "min_direct_booking_date",
    ),
    jsonArrayFrom(
      eb
        .selectFrom("form")
        // .where("form.establishment_id", "=", eb.ref("establishment.id"))
        .where((eb) => eb.or([eb("form.establishment_id", "=", eb.ref("establishment.id")), eb("form.establishment_id", "is", null)]))
        .where((eb) => {
          const latestCmpr = eb("form.deleted_at", "=", at_infinity_value);
          if (state.form_id) return eb.or([latestCmpr, eb("form.id", "=", state.form_id)]);
          return latestCmpr;
        })
        .selectAll("form")
        .select((eb) => jsonArrayFrom(eb.selectFrom("field").whereRef("field.form_id", "=", "form.id").selectAll("field")).as("fields")),
    ).as("forms"),
  ]);

  const bookingWithActivites = bookingQb(kysely).select((eb) => [
    jsonArrayFrom(
      saleItemWithProductQb
        .where("sale_item.duration", "is not", null)
        .where("sale_item.booking_id", "=", eb.ref("booking.id"))
        .select((eb) => [
          jsonArrayFrom(
            eb.selectFrom("participation").selectAll("participation").where("participation.sale_item_id", "=", eb.ref("sale_item.id")),
          ).as("participations"),
        ]),
    ).as("activities"),
    jsonArrayFrom(
      eb
        .selectFrom("participant")
        .where("participant.booking_id", "=", eb.ref("booking.id"))
        .select((eb) => [
          "participant.id",
          "participant.customer_id",
          eb
            .selectFrom("participation")
            .where("participation.participant_id", "=", eb.ref("participant.id"))
            .select((eb) => arrayAgg(eb.ref("participation.sale_item_id"), "uuid").as("ids"))
            .as("sale_item_ids"),
        ]),
    ).as("participants"),
    eb.selectFrom("invoice").where("invoice.booking_id", "=", eb.ref("booking.id")).select("invoice.id").limit(1).as("invoice_id"),
    jsonObjectFrom(finalEstablishmentQb.where("establishment.id", "=", eb.ref("booking.establishment_id"))).as("establishment"),
  ]);

  const context = await kysely
    .selectNoFrom((eb) => {
      const personSearchQb = createPersonSearchQb(state.search)
        .innerJoin("customer as s_customer", "s_customer.person_id", "s_person.id")
        .where("s_customer.id", "in", (eb) =>
          eb
            .selectFrom("participant")
            .where((eb) =>
              eb.or([
                eb("participant.id", "in", eb.selectFrom("participation").select("participation.participant_id")),
                eb("participant.booking_id", "is not", null),
              ]),
            )
            .select("participant.customer_id"),
        )
        .where((eb) =>
          eb.or([
            eb("s_customer.establishment_id", "=", state.persist_establishment_id || null),
            eb(
              "s_customer.establishment_id",
              "=",
              eb
                .selectFrom("product")
                .innerJoin("item", "item.id", "product.item_id")
                .where("product.id", "=", state.product_id || null)
                .select("item.establishment_id"),
            ),
          ]),
        );

      const myUserIdsQb = activeUserSessionSimple(kysely, ctx.session_id, true).select("_user_session.user_id");
      const allMyRegisteredCustomerQb = personSearchQb.where("s_person.user_id", "=", myUserIdsQb);

      const myEstablishmentIds = memberIsAdminOrOwnerQb(qbArgs).select("_member.establishment_id");
      const establishmentParticipants = personSearchQb.where("s_customer.establishment_id", "in", myEstablishmentIds);

      const formWaiversQb = eb
        .selectFrom("form")
        .innerJoin("form_waiver", "form_waiver.form_id", "form.id")
        .where((eb) => {
          if (state.form_id) return eb("form.id", "=", state.form_id);
          const bookingFormIdsQb = eb
            .selectFrom("booking")
            .innerJoin("sale_item", "sale_item.booking_id", "booking.id")
            .select("sale_item.form_id");

          if (state.id) {
            return eb.or([
              eb(
                "form.id",
                "in",
                bookingFormIdsQb.where(
                  "sale_item.id",
                  "in",
                  eb.selectFrom("participation").where("participation.participant_id", "=", state.id).select("participation.sale_item_id"),
                ),
              ),
              eb("form.id", "=", eb.selectFrom("participant").where("participant.id", "=", state.id).select("participant.form_id")),
            ]);
          }

          if (state.booking_id) {
            return eb("form.id", "in", bookingFormIdsQb.where("booking.id", "=", state.booking_id));
          }

          if (state.product_id) {
            return eb.and([
              eb("form.deleted_at", "=", at_infinity_value),
              eb(
                "form.root_id",
                "=",
                eb
                  .selectFrom("product")
                  .innerJoin("item", "item.id", "product.item_id")
                  .select("item.form_root_id")
                  .where("product.id", "=", state.product_id),
              ),
            ]);
          }

          return eb("form.id", "is", null);
        });

      return [
        jsonArrayFrom(memberQb(qbArgs).select("_member.establishment_id")).as("my_members"),
        jsonArrayFrom(
          eb
            .selectFrom("waiver")
            .select((eb) => [
              "waiver.id",
              "waiver.description",
              jsonObjectFrom(
                eb
                  .selectFrom("waiver_translation")
                  .leftJoin(unnestArray(locales, "locale"), "locale.key", "waiver_translation.language_code")
                  .selectAll("waiver_translation")
                  .where("waiver_translation.waiver_id", "=", eb.ref("waiver.id"))
                  .orderBy("locale.pos", ascNullsLast)
                  .orderBy("waiver_translation.sort_order", ascNullsLast)
                  .limit(1),
              ).as("translation"),
              formWaiversQb
                .select((eb) => arrayAggDisinct(eb.ref("form.id")).as("form_ids"))
                .where("form_waiver.waiver_id", "=", eb.ref("waiver.id"))
                .as("form_ids"),
            ])
            .where("waiver.type", "=", "read" satisfies WaiverType)
            .where("waiver.id", "in", formWaiversQb.select("form_waiver.waiver_id")),
        ).as("read_waivers"),
        jsonObjectFrom(
          baseProductQb
            .selectAll("item")
            .selectAll("product")
            .select((eb) => [
              eb
                .selectFrom("form")
                .where("form.root_id", "=", eb.ref("item.form_root_id"))
                .where("form.deleted_at", "=", at_infinity_value)
                .select("form.id")
                .as("form_id"),
              jsonArrayFrom(
                eb
                  .selectFrom("addon")
                  .where("addon.id", "=", eb.fn.any("item.addon_ids"))
                  .select((eb) =>
                    notNull(
                      jsonObjectFrom(
                        eb.selectFrom("price").select(["price.amount", "price.currency_id"]).whereRef("price.id", "=", "addon.price_id"),
                      ),
                    ).as("price"),
                  )
                  .selectAll("addon"),
              ).as("addons"),
              divingCoursesJsonEb,
              divingLocationsJsonEb,
              divingLevelsJsonEb,
              pricesJsonEb,
              notNull(jsonObjectFrom(finalEstablishmentQb.where("establishment.id", "=", eb.ref("item.establishment_id")))).as(
                "establishment",
              ),
            ])
            .where("product.id", "=", state.product_id),
        ).as("product"),
        jsonObjectFrom(finalEstablishmentQb.where("establishment.id", "=", state.persist_establishment_id || null)).as("establishment"),
        jsonObjectFrom(bookingWithActivites.where("booking.id", "=", state.booking_id || null)).as("booking"),
        jsonObjectFrom(
          participantQb(kysely)
            .innerJoin("establishment", "establishment.id", "customer.establishment_id")
            .leftJoin("spot", "spot.id", "establishment.spot_id")
            .leftJoin("region", "region.id", "spot.region_id")
            .where("participant.id", "=", state.id)
            .$if(!isCopy, (eb) =>
              eb.select((participantEb) => [
                jsonArrayFrom(
                  participationWaiversQb.select(["sale_item.id as sale_item_id"]).where("waiver.type", "=", "read" satisfies WaiverType),
                ).as("participant_waivers"),
                jsonObjectFrom(
                  bookingWithActivites
                    .leftJoin("sale_item", "sale_item.id", "sale_item.booking_id")
                    .leftJoin("participation", "participation.sale_item_id", "sale_item.id")
                    .where((eb) =>
                      eb.or([
                        eb("participation.participant_id", "=", participantEb.ref("participant.id")),
                        eb("booking.id", "=", participantEb.ref("participant.booking_id")),
                      ]),
                    )
                    .limit(1),
                ).as("booking"),
              ]),
            )
            .select((eb) => [
              jsonArrayFrom(participationQb.where("participation.participant_id", "=", eb.ref("participant.id"))).as("participations"),
              jsonObjectFrom(finalEstablishmentQb.where("establishment.id", "=", eb.ref("customer.establishment_id"))).as("establishment"),
              eb("participant.id", "in", allowedParticipantsForMember(qbArgs).select("_participant.id")).as("allowed_for_member"),
              eb(
                "participant.id",
                "in",
                myRegisteredParticipants(qbArgs)
                  .union(
                    kysely
                      .selectFrom("session_link")
                      .where("session_link.session_id", "=", ctx.session_id)
                      .select("session_link.participant_id"),
                  )
                  .select("_participant.id"),
              ).as("allowed_for_me"),
              // eb
              //   .exists(
              //     allowedParticipantsForMeOrMember(kysely, ctx.session_id)
              //       .union(
              //         kysely
              //           .selectFrom("session_link")
              //           .where("session_link.session_id", "=", ctx.session_id)
              //           .select("session_link.target_id"),
              //       )
              //       .select("_participant.id")
              //       .where("_participant.id", "=", eb.ref("participant.id")),
              //   )
              //   .as("allowed"),
            ]),
        ).as("participant"),
        allMyRegisteredCustomerQb
          .clearOrderBy()
          .select((eb) => eb.fn.count<number>("s_person.id").as("participants_count"))
          .as("my_participants_count"),
        jsonArrayFrom(
          allMyRegisteredCustomerQb
            .select((eb) =>
              jsonArrayFrom(
                participantQb(kysely)
                  .where("customer.person_id", "=", eb.ref("s_person.id"))
                  .clearOrderBy()
                  .orderBy("participant.created_at", "desc"),
              ).as("participants"),
            )
            .offset(state.page_nr * itemPerPage)
            .limit(itemPerPage),
        ).as("my_participants"),
        establishmentParticipants
          .clearOrderBy()
          .select((eb) => eb.fn.count<number>("s_customer.id").as("participants_count"))
          .as("establishment_participants_count"),
        jsonArrayFrom(
          establishmentParticipants
            .select((eb) => [
              "s_person.user_id",
              jsonArrayFrom(
                participantQb(kysely)
                  .where((eb) =>
                    eb.or([
                      eb("participant.id", "in", eb.selectFrom("participation").select("participation.participant_id")),
                      eb("participant.booking_id", "is not", null),
                    ]),
                  )
                  .where("participant.customer_id", "=", eb.ref("s_customer.id"))
                  .clearOrderBy()
                  .orderBy("participant.created_at", "desc"),
              ).as("participants"),
            ])
            .offset(state.page_nr * itemPerPage)
            .limit(itemPerPage),
        ).as("establishment_participants"),
        jsonArrayFrom(
          participantQb(kysely)
            .where("customer.establishment_id", "=", state.persist_establishment_id || null)
            .where("customer.establishment_id", "in", myEstablishmentIds)
            .where("participant.booking_id", "is", null)
            .where("participant.id", "not in", participatingParticipantIdQb),
        ).as("pending_participants"),
      ];
    })
    .executeTakeFirstOrThrow();

  // if (!context) throw unauthorized();
  if (state.persist_participant_id && !context.participant) throw notFound();

  const booking = context.participant?.booking || context.booking;
  const product = context.product;
  const establishment = booking?.establishment || context.participant?.establishment || product?.establishment || context.establishment;

  const simple_view = !context.my_members.find((member) => member.establishment_id === establishment?.id);

  return {
    ...context,
    ...createPageOverwrites({
      simple_view: simple_view,
      show_whatsapp: false,
      customer_toggle: true,
      establishment_id: establishment?.id || null,
    }),
    booking: booking,
    establishment: establishment,
    isCopy: isCopy,
  };
};

export const meta = createMeta({ title: "Participant Registration" });

const scrollToDivId = "scroll-to-div";

export const SelectParticipant = (props: {
  participants: {
    items: {
      participants: {
        id: string;
        customer_id: string | null;
        first_name: string | null;
        last_name: string | null;
        email: string | null;
      }[];
    }[];
    count: number | null;
  };
}) => {
  const context = useAppContext();
  const search = useSearchParams2();
  // search.ispe
  const response = useLoaderData<typeof loader>();
  const selectedParticipant = response.participant;

  const participantSelect = search.state.participant_select;

  const participantCount = props.participants.count || 0;
  const nrOfPages = Math.ceil(participantCount / itemPerPage);
  const pageNr = search.state.page_nr;
  const nextPage = pageNr + 1;

  const pendingParticipants = response.pending_participants;
  // const participants = participantSelect === 'pending' ? pendingParticipants : props.p

  const searchStr = search.state.search;

  const showPaging = participantCount > 9 || !!searchStr;

  return (
    <AnimatingDiv className="space-y-3 py-6">
      <AnimatingDiv className="flex flex-wrap gap-3 items-center">
        {participantSelect !== "pending" && (
          <ParamLink
            className="btn btn-primary whitespace-nowrap"
            paramState={{ participant_select: participantSelect ? undefined : "normal" }}
          >
            {participantSelect
              ? `Hide ${props.participants.count} results`
              : selectedParticipant
                ? selectedParticipant.first_name + " " + selectedParticipant.last_name + " selected"
                : "Select existing registration"}
          </ParamLink>
        )}
        {participantSelect !== "normal" && !search.state.customer_view && pendingParticipants.length > 0 && (
          <ParamLink
            paramState={{ participant_select: participantSelect ? undefined : "pending" }}
            className="btn btn-red whitespace-nowrap font-bold"
          >
            {participantSelect === "pending"
              ? `hide ${pendingParticipants.length} result(s)`
              : `${pendingParticipants.length} pending participant(s)`}
          </ParamLink>
        )}
        {selectedParticipant && (
          <ParamLink
            path={"./"}
            paramState={{
              id: undefined,
              action_type: undefined,
              participant_select: undefined,
            }}
            className="link"
          >
            clear
          </ParamLink>
        )}
      </AnimatingDiv>
      {participantSelect === "pending" && (
        <div>
          {pendingParticipants.map((participant) => {
            return (
              <ParamLink
                key={participant.id}
                type={"button"}
                path={"./"}
                aria-busy={search.pendingState?.id === participant.id}
                paramState={{
                  id: participant.id,
                  participant_select: undefined,
                }}
                className="flex text-left flex-row gap-3 items-center hover:bg-slate-50 text-slate-800 rounded-md w-full aria-busy:spinner spinner-dark relative"
              >
                <div className="p-2 rounded-full bg-slate-200">
                  <UserIcon className="w-8 h-8 text-slate-600" />
                </div>
                <div>
                  <p className="font-semibold line-clamp-1">
                    {participant.first_name} {participant.last_name}
                  </p>
                  <p className="text-slate-500 text-xs">{participant.email}</p>
                </div>
                <div className="flex-1" />
                {/*<ReturningCustomerBadge participant_count={participantCount} />*/}
                <ChevronRightIcon className="w-5 h-5" />
              </ParamLink>
            );
          })}
        </div>
      )}
      {participantSelect === "normal" && (
        <div className={twMerge("space-y-3 border-b border-slate-300", !showPaging && "pb-3")}>
          <p className="text-slate-700">Select to retrieve the previous registration along with any associated waivers.</p>
          {showPaging && <SearchField></SearchField>}
          {/*<p className="">{response.participants_count} results</p>*/}
          <div className="grid lg:grid-cols-3 gap-3">
            {!props.participants.count && <div>No Results</div>}
            {props.participants.items.map((participantGroup, index) => {
              // console.log("particgroup", participantGroup);
              // const participantGroupIdentifier = "" + participantGroup.first_name + participantGroup.last_name + participantGroup.email;
              const mostRecentRegistration = participantGroup.participants[0];
              if (!mostRecentRegistration) return <div key={index}>could not find most recent registration</div>;

              const existingParticipantInThisBooking = response.booking?.participants.find(
                (participant) => participant.customer_id === mostRecentRegistration.customer_id,
              );
              // const participantCount = Number(participantGroup.count);
              return (
                <div key={mostRecentRegistration.id}>
                  <ParamLink
                    type={"button"}
                    path={"./"}
                    reload
                    aria-busy={search.pendingState?.id === mostRecentRegistration.id}
                    paramState={{
                      id: existingParticipantInThisBooking ? existingParticipantInThisBooking.id : mostRecentRegistration.id,
                      toggle_sale_item_ids: search.state.toggle_sale_item_ids.filter(
                        (activityId) =>
                          !existingParticipantInThisBooking?.sale_item_ids?.find((existingActiviytId) => existingActiviytId === activityId),
                      ),
                      action_type: existingParticipantInThisBooking ? undefined : "copy",
                      participant_select: undefined,
                    }}
                    className="flex text-left flex-row gap-3 items-center hover:bg-slate-50 text-slate-800 rounded-md w-full aria-busy:spinner spinner-dark relative"
                  >
                    <div className="p-2 rounded-full bg-slate-200">
                      <UserIcon className="w-8 h-8 text-slate-600" />
                    </div>
                    <div>
                      <p className="font-semibold line-clamp-1">
                        {mostRecentRegistration.first_name} {mostRecentRegistration.last_name}
                      </p>
                      <p className="text-slate-500 text-xs">{mostRecentRegistration.email}</p>
                    </div>
                    <div className="flex-1" />
                    {/*<ReturningCustomerBadge participant_count={participantCount} />*/}
                    <ChevronRightIcon className="w-5 h-5" />
                  </ParamLink>
                </div>
              );
            })}
          </div>
          {showPaging && (
            <div className="flex flex-row items-center gap-3 justify-between py-3">
              <ParamLink
                className="link aria-disabled:text-slate-300 aria-disabled:cursor-default aria-disabled:no-underline"
                path="./"
                paramState={{ page_nr: pageNr - 1 }}
                preventScrollReset
                aria-disabled={!pageNr}
              >
                <ChevronLeftIcon className="w-5 h-5 inline-block" /> Previous
              </ParamLink>
              <span>
                {pageNr + 1} of {nrOfPages}
              </span>
              <ParamLink
                className="link aria-disabled:text-slate-300 aria-disabled:cursor-default aria-disabled:no-underline"
                path="./"
                preventScrollReset
                paramState={{ page_nr: nextPage < nrOfPages ? nextPage : nrOfPages - 1 }}
                aria-disabled={!(nextPage < nrOfPages)}
              >
                Next <ChevronRightIcon className="w-5 h-5 inline-block" />
              </ParamLink>
            </div>
          )}
        </div>
      )}
    </AnimatingDiv>
  );
  // if (response.lastRegistration.length > 0)
};

interface AddonCustom {
  id: string;
  name: string;
  unit: string;
  addon_id: string;
  sale_item_id: string;
  allow_change: boolean;
  price_amount: number;
  quantity: number;
  index: string;
}

export default function Page() {
  const context = useAppContext();
  const search = useSearchParams2();
  const response = useLoaderData<typeof loader>();
  const lingui = useLingui();
  const emailEdit = useBoolean();

  const participant = response.participant;
  const booking = response.booking;
  const product = response.product;
  const productFirstPrice = product?.product_prices[0];

  const establishment = response.establishment;
  const defaultCurrencyId = booking?.currency_id || establishment?.default_currency || defaultCurrency;
  const startDateInputId = useId();

  const isManager = !!context.members.find(
    (member) => member.admin >= getAdminLevelIndex("write") && member.establishment_id === establishment?.id,
  );

  const participants = isManager
    ? {
        count: response.establishment_participants_count,
        items: response.establishment_participants,
      }
    : { count: response.my_participants_count, items: response.my_participants };

  const availableForms = establishment?.forms || [];
  const overwriteFieldsConfig = availableForms.find((form) => form.id === search.state.form_id)?.fields;

  const existingActivitesMapped =
    booking?.activities.map((activity) => {
      const available = activity.participations.find((participation) => !participation.participant_id);
      const foundParticipation = participant?.participations.find((participation) => participation.sale_item_id === activity.id);
      const alreadySelected = !response.isCopy && foundParticipation;
      const defaultChecked = response.isCopy ? available && alreadySelected : alreadySelected;
      const toggledActivity = search.state.toggle_sale_item_ids.includes(activity.id);
      const checked = !!defaultChecked === !toggledActivity;
      return {
        activity: {
          ...activity,
          index: "activity-" + activity.id,
          addons: activity.activity_addons.map((addon) => ({
            ...addon,
            allow_change: addon.allow_change,
            index: activity.id + addon.id,
          })) satisfies AddonCustom[],
          prices: [{ currency_id: defaultCurrencyId, amount: activity.price_pp }],
        },
        selected: checked,
      };
    }) || [];

  const productsMaps = product
    ? [
        {
          selected: true,
          activity: {
            id: null,
            description: null,
            index: "product-" + product.id,
            product_id: product.id,
            booking_id: "",
            duration_start: null,
            duration_end: null,
            prices: product.product_prices,
            discount_percentage: 0,
            exclude_discount_for_addons: false,
            duration: product.duration_in_hours,
            form_id: product.form_id,
            product: product,
            addons: product.addons.map((addon) => ({
              allow_change: addon.allow_change,
              index: product.id + addon.id,
              id: "",
              price_amount: addon.price?.amount || 0,
              sale_item_id: "",
              addon_id: addon.id,
              name: addon.name,
              quantity: addon.quantity,
              unit: addon.unit,
            })) satisfies AddonCustom[],
            registration_form: availableForms.find((form) => form.id === product.form_id) || null,
            participations: null,
          } as const,
        },
      ]
    : [];

  const activitesMapped = [...existingActivitesMapped, ...productsMaps];

  const selectedActivities = activitesMapped.filter((activity) => activity.selected).map((activity) => activity.activity);

  const selectedActivitiesWithSelectableAddons = selectedActivities.filter(
    (activity) => activity.addons.filter((addon) => addon.allow_change).length > 0,
  );

  const activityForms = selectedActivities.map((activity) => activity?.registration_form).filter(Boolean);
  const fieldsPerProduct = activityForms.map((form) => form?.fields || []);
  const fields = flat(fieldsPerProduct || []);
  console.log("activityForms", activityForms);

  const i18n = useLingui();

  const waivers = response.read_waivers.filter((waiver) => {
    return waiver.form_ids?.find((formId) => {
      if (search.state.form_id) return search.state.form_id === formId;
      const fromSelectedActivity = selectedActivities.find((activity) => activity.form_id === formId);
      const fromParticipant = participant?.form_id === formId;
      return fromSelectedActivity || fromParticipant;
    });
  });

  const fieldsContext = useMemo(() => {
    return {
      ...response,
      i18n: i18n,
    };
  }, [i18n, response]);

  return (
    <Fragment>
      <CDialog dialogname={"content"} className="space-y-6">
        <div className="flex flex-row items-center justify-between gap-3 font-bold">
          {context.verified_at ? <p>Already logged in</p> : <p className="text-xl">Verify Your Email</p>}
          <DialogCloseButton />
        </div>
        {context.user_id ? (
          <div>
            {context.verified_at ? (
              <div>
                <ParamLink className="w-full text-center link" paramState={{ modal_detail_name: undefined, participant_select: "normal" }}>
                  Select Existing Registration
                </ParamLink>
              </div>
            ) : (
              <div className="space-y-6">
                <OtpBlock
                  user_id={context.user_id}
                  verifyOTPComp={
                    <RedirectParamsInput
                      path={"./"}
                      paramState={{
                        toggle_modal: undefined,
                        participant_select: "normal",
                      }}
                    />
                  }
                />
                <OtpGoBackBlock />
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            <p>Retrieve your previous registration by verifying your email.</p>
            <ActionForm className={"space-y-3"}>
              <RegisterEmailInput />
              <RInput table={"user_session"} field={"data.method"} value={"otp" satisfies AuthzMethod} type={"hidden"} />
              <RInput table={"session"} field={"data.selected_user_id"} type={"hidden"} value={tableIdRef("user")} />
              <RInput table={"user_session"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
              <RInput table={"one_time_password"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
              <SubmitButton className="btn btn-primary w-full">Send a One Time Password</SubmitButton>
            </ActionForm>
            <hr />
            <p className="text-sm">
              By continuing, you agree to the <span className="capitalize">{diversdeskName}</span>
              <TermsLink /> and <PolicyLink />
              <br />
              Powered by <span className="capitalize">{diversdeskName}</span>
            </p>
          </div>
        )}
      </CDialog>
      <SearchForm />
      <ActionForm
        className="py-6"
        withPing
        preventScrollReset={false}
        onSubmit={(e) => {
          if (activitesMapped.length > 0 && !activitesMapped.find((activity) => activity.selected)) {
            e.preventDefault();
            window.alert("Select at least one activity");
          }
        }}
      >
        <div className="app-container space-y-3">
          <div className="flex-1">
            <div className="text-right flex flex-row items-center justify-end gap-1">
              <span className="text-slate-500 text-xs">
                <Trans>Preferred language</Trans>
              </span>
              &nbsp;
              <ParamLink className="link uppercase" replace paramState={{ toggle_modal: "language" }}>
                {context.locale}
              </ParamLink>
            </div>
          </div>
          <ActionAlert scrollTo />
          {establishment && <h2 className="text-xl font-bold text-slate-800">{getEstablishmentName(establishment)}</h2>}
          {booking && (
            <Fragment>
              <ParamLink path={_booking_detail(booking.id)} paramState={{ customer_view: search.state.customer_view }} className="link">
                <Trans>Booking</Trans> {booking.booking_reference || booking.sqid}
              </ParamLink>
              <RInput table={"booking"} field={"id"} value={booking.id} />
            </Fragment>
          )}
          {product && !booking ? (
            <Fragment>
              <RInput table={"booking"} field={"data.establishment_id"} type={"hidden"} value={product?.establishment_id || ""} />
              <RInput table={"booking"} field={"data.direct_booking"} hiddenType={"__boolean__"} type={"hidden"} value={"true"} />
              <RInput
                table={"booking"}
                field={"data.meeting_type"}
                type={"hidden"}
                value={establishment?.default_booking_meeting_type || ("DIVE_CENTER" satisfies keyof typeof meetingTypes)}
              />
              <RInput
                table={"booking"}
                field={"data.meeting_time"}
                type={"hidden"}
                value={establishment?.default_booking_meeting_time || ""}
              />
              <RInput
                table={"booking"}
                field={"data.booking_reference"}
                type={"hidden"}
                value={fName("person", "data.first_name") + " " + fName("person", "data.last_name")}
              />
              <RInput
                table={"booking"}
                field={"data.currency_id"}
                value={productFirstPrice?.currency_id || defaultCurrencyId}
                type={"hidden"}
              />
            </Fragment>
          ) : (
            <OperationInput table={"booking"} value={"ignore"} />
          )}
          {activitesMapped.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-xl text-secondary-500 font-semibold">
                <Trans>Please select the activity (or activities) you're attending.</Trans>
              </h3>
              <div className="space-y-4">
                {activitesMapped.map(({ activity, selected }) => {
                  const product = activity.product;
                  const productPrices = product?.product_prices || [];
                  const finalPricees = productPrices.length
                    ? productPrices
                    : [{ currency_id: establishment?.default_currency || defaultCurrencyId, amount: 0 }];
                  const firstPrice = finalPricees[0];
                  const checked = selected;
                  const participations = activity.participations || [];
                  const myParticipation = participations.find(
                    (participation) => participant && !response.isCopy && participant.id === participation.participant_id,
                  );
                  const availableParticipations = participations.filter((participation) => !participation.participant_id);
                  const fieldIndex = activity.index;
                  const activitySlug = getActivitySlug(product?.activity_slug);
                  // const defaultPriceCurrency = firstPrice?.currency_id || establishment?.default_currency || defaultCurrency;

                  if (activity.id && availableParticipations.length === 0 && !myParticipation)
                    return (
                      <div key={fieldIndex} className="opacity-50 space-y-1">
                        <div className="flex flex-row gap-3 justify-between">
                          <p className="font-bold">{activitySlug ? activities[activitySlug].name : "Custom"} (all spots taken)</p>
                          <p>{formatDuration(activity)}</p>
                        </div>
                        <ProductItem
                          item={{
                            title: activity?.description,
                            ...product,
                            product_prices: finalPricees,
                          }}
                          locale={establishment?.locale || null}
                        />
                      </div>
                    );
                  return (
                    <div key={fieldIndex} className="space-y-1">
                      <div className="flex flex-row gap-3 justify-between">
                        <p className="font-bold">{activitySlug ? activities[activitySlug].name : "Custom"}</p>
                        <p>{formatDuration(activity)}</p>
                      </div>
                      <ParamLink
                        aria-busy={false}
                        paramState={{ toggle_sale_item_ids: toggleArray(search.state.toggle_sale_item_ids, activity.id) }}
                        aria-selected={checked}
                        aria-disabled={!activity.id}
                        className="flex group flex-row bg-secondary-50 rounded-md aria-selected:outline outline-2 outline-secondary-500 items-center pl-3"
                      >
                        <Checker />
                        <ProductItem
                          item={{
                            title: activity?.description,
                            ...product,
                            product_prices: [],
                          }}
                          locale={establishment?.locale || null}
                        />
                      </ParamLink>
                      {checked ? (
                        <Fragment>
                          {myParticipation ? (
                            <Fragment>
                              <RInput table={"participation"} field={"id"} value={myParticipation.id} type={"hidden"} index={fieldIndex} />
                              <OperationInput table={"participation"} value={"ignore"} index={fieldIndex} />
                            </Fragment>
                          ) : activity.id ? (
                            <Fragment>
                              <OperationInput table={"sale_item"} value={"ignore"} index={fieldIndex} />
                              <RInput table={"sale_item"} field="id" value={activity.id} index={fieldIndex} />
                              <OperationInput table={"participation"} value={"update"} index={fieldIndex} />
                              <RInput
                                table={"participation"}
                                field={"data.sale_item_id"}
                                value={tableIdRef("sale_item", fieldIndex)}
                                type={"hidden"}
                                index={fieldIndex}
                              />
                              <RInput
                                table={"participation"}
                                field={"data.participant_id"}
                                value={tableIdRef("participant")}
                                type={"hidden"}
                                index={fieldIndex}
                              />
                            </Fragment>
                          ) : (
                            <div className="space-y-3 pb-3">
                              <RInput
                                table={"sale_item"}
                                field={"data.price_pp"}
                                value={firstPrice?.amount}
                                type={"hidden"}
                                index={fieldIndex}
                              />
                              <RInput
                                table={"sale_item"}
                                field={"data.form_id"}
                                type={"hidden"}
                                index={fieldIndex}
                                value={activity.form_id || ""}
                              />
                              <RInput
                                table={"sale_item"}
                                field={"data.product_id"}
                                type={"hidden"}
                                index={fieldIndex}
                                value={product?.id}
                              />
                              <RInput
                                table={"sale_item"}
                                field={"data.booking_id"}
                                type={"hidden"}
                                index={fieldIndex}
                                value={tableIdRef("booking")}
                              />
                              <input
                                type={"hidden"}
                                name={fName("sale_item", "data.duration", fieldIndex, "duration")}
                                value={getDurationInDays(product?.duration_in_hours)}
                              />
                              <HiddenTypeInput name={fName("sale_item", "data.duration", fieldIndex)} value={"__pg_daterange"} />
                              <RInput
                                table={"participation"}
                                field={"data.sale_item_id"}
                                value={tableIdRef("sale_item", fieldIndex)}
                                type={"hidden"}
                                index={fieldIndex}
                              />
                              <RInput
                                table={"participation"}
                                field={"data.participant_id"}
                                value={tableIdRef("participant")}
                                type={"hidden"}
                                index={fieldIndex}
                              />

                              {activity.addons.map((addon) => {
                                const addonFieldIndex = addon.index;
                                return (
                                  <Fragment key={addonFieldIndex}>
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.addon_id"}
                                      type={"hidden"}
                                      value={addon.addon_id}
                                      index={addonFieldIndex}
                                    />
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.sale_item_id"}
                                      type={"hidden"}
                                      value={tableIdRef("sale_item", fieldIndex)}
                                      index={addonFieldIndex}
                                    />
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.name"}
                                      type={"hidden"}
                                      value={addon.name}
                                      index={addonFieldIndex}
                                    />
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.price_amount"}
                                      type={"hidden"}
                                      value={addon.price_amount + ""}
                                      index={addonFieldIndex}
                                    />
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.unit"}
                                      type={"hidden"}
                                      value={addon.unit}
                                      index={addonFieldIndex}
                                    />
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.allow_change"}
                                      type={"hidden"}
                                      hiddenType={"__boolean__"}
                                      value={addon.allow_change + ""}
                                      index={addonFieldIndex}
                                    />
                                    <RInput
                                      table={"activity_addon"}
                                      field={"data.quantity"}
                                      type={"hidden"}
                                      value={addon.quantity}
                                      index={addonFieldIndex}
                                    />
                                  </Fragment>
                                );
                              })}
                              <div className="grid md:grid-cols-2 gap-3">
                                <div className="flex-1">
                                  <label htmlFor={startDateInputId}>Start date</label>
                                  <br />
                                  <SimpleRInput
                                    id={startDateInputId}
                                    type="date"
                                    min={establishment?.min_direct_booking_date}
                                    required
                                    className={"input"}
                                    name={fName("sale_item", "data.duration", fieldIndex, "date")}
                                    onBlur={(e) => {
                                      const selectedDate = new Date(e.target.value);
                                      const today = new Date(context.date.todayParam);
                                      if (selectedDate < today) {
                                        alert("The chosen date must be in the future. Please adjust your selection.");
                                      }
                                    }}
                                  />
                                </div>
                                <div>
                                  <RLabel table={"sale_item"} field={"data.quantity"} index={fieldIndex} className={"required"}>
                                    Nr of participants
                                  </RLabel>
                                  <br />
                                  <RSelect
                                    className="select w-full"
                                    required
                                    defaultValue={1}
                                    table={"sale_item"}
                                    field={"data.quantity"}
                                    index={fieldIndex}
                                  >
                                    {participantsNrArray.map((nr) => (
                                      <option key={nr} value={nr + ""}>
                                        {nr}
                                      </option>
                                    ))}
                                  </RSelect>
                                </div>
                              </div>
                            </div>
                          )}
                        </Fragment>
                      ) : (
                        <Fragment>
                          {myParticipation && (
                            <Fragment>
                              <RInput table={"participation"} field={"id"} value={myParticipation.id} index={fieldIndex} />
                              <OperationInput table={"participation"} value={"update"} index={fieldIndex} />
                              <RInput table={"participation"} field={"data.participant_id"} value={""} type={"hidden"} index={fieldIndex} />
                            </Fragment>
                          )}
                        </Fragment>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          {participant ? (
            <div>
              <h2 className="text-xl font-bold text-slate-600">
                {response.isCopy ? <Trans>Copy</Trans> : <Trans>Edit</Trans>} Participant {participant.first_name} {participant.last_name}
              </h2>
              <p className="text-xs text-slate-500">Registered by {participant.user?.email}</p>
            </div>
          ) : (
            <div>
              <h2 className="text-xl font-bold text-slate-600">
                <Trans>Register</Trans>
              </h2>
            </div>
          )}
          <RedirectParamsInput
            path={_participant_detail(tableIdRef("participant"))}
            // path={search.state.booking_id ? _booking_detail(search.state.booking_id) : _participant_detail(tableRef("participant"))}
            paramState={{}}
          />
          <RInput table={"participant"} field={"data.booking_id"} type={"hidden"} value={tableIdRef("booking")} />
          <RInput table={"customer"} field={"data.establishment_id"} value={establishment?.id} type={"hidden"} />
          <RInput table={"customer"} field={"data.person_id"} value={tableIdRef("person")} type={"hidden"} />
          <RInput table={"person"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
          <RInput table={"participant"} field={"data.customer_id"} value={tableIdRef("customer")} type={"hidden"} />
          {participant && !response.isCopy ? (
            <Fragment>
              <RInput table={"participant"} field={"id"} value={participant.id} />
              <OperationInput table={"participant"} value={"update"} />
            </Fragment>
          ) : (
            <Fragment>
              <CallbackInput callbackName={"send_participant_registration_email"} target_id={tableIdRef("participant")} />
              {!isManager && (
                <CallbackInput
                  callbackName={"send_participant_registration_email_to_establishment"}
                  target_id={tableIdRef("participant")}
                />
              )}
            </Fragment>
          )}
          <div id={scrollToDivId} />
        </div>
        <div className="space-y-6">
          <div className="app-container space-y-3">
            {(!participant || response.isCopy || !!search.state.booking_id) && (
              <AnimatingDiv>
                {!!context.verified_at && (participants.items.length > 0 || !!search.state.search) && !search.state.customer_view && (
                  <SelectParticipant participants={participants} />
                )}
                {(!context.email || !context.verified_at || search.state.customer_view) && (
                  <div className="flex flex-row gap-3 items-center">
                    <span className="text-slate-500">
                      <Trans>Returning participant? Retrieve your registration via OTP email verification.</Trans>
                    </span>
                    <Tooltip description={search.state.customer_view && "OTP is disabled when Customer View is active."}>
                      <ParamLink
                        aria-disabled={search.state.customer_view}
                        className={"btn btn-primary whitespace-nowrap"}
                        paramState={{
                          toggle_modal: "content",
                        }}
                      >
                        <Trans>Email OTP</Trans>
                      </ParamLink>
                    </Tooltip>
                  </div>
                )}
                {response.isCopy && participant && (
                  <div className="flex flex-row gap-3 items-center">
                    <CheckDoneIcon className="w-8 h-8 text-green-500" />
                    <p className="text-slate-700">
                      Previous registration retrieved.
                      <br />
                      Please review and update any necessary details.
                    </p>
                  </div>
                )}
              </AnimatingDiv>
            )}
            {(!participant || response.isCopy) && isManager && !search.state.customer_view && (
              <div className="flex flex-wrap gap-3">
                <ParamLink
                  paramState={{ form_id: null }}
                  aria-selected={!overwriteFieldsConfig}
                  className="btn btn-basic opacity-50 aria-selected:opacity-100 aria-selected:bg-slate-200"
                >
                  Default template
                </ParamLink>
                {availableForms.map((form) => (
                  <ParamLink
                    key={form.id}
                    aria-selected={form.id === search.state.form_id}
                    paramState={{ form_id: form.id }}
                    className={twMerge(
                      "btn btn-basic opacity-50 aria-selected:opacity-100 aria-selected:bg-slate-200",
                      activityForms.find((activityForm) => activityForm?.id === form.id) && "bg-slate-400 opacity-100",
                    )}
                    type={"button"}
                  >
                    {form.name}
                  </ParamLink>
                ))}
              </div>
            )}
          </div>
          <div key={participant?.id || ""} className="space-y-3">
            <Suspense fallback={<div>Could not render recipient email</div>}>
              <div className="app-container space-y-3">
                <div>
                  <div className="flex flex-wrap gap-3 items-center">
                    <RLabel table={"user"} field={"data.email"}>
                      <Trans>Recipient email</Trans>
                    </RLabel>
                    {/*<button type={"button"} onClick={emailEdit.toggle}>*/}
                    {/*  edit*/}
                    {/*</button>*/}
                  </div>
                  <RInput
                    table={"user"}
                    field={"data.email"}
                    placeholder="email"
                    className={"input"}
                    required
                    readOnly={!!participant && !emailEdit.isOn}
                    defaultValue={participant?.user?.email || context.email || ""}
                  />
                  <RInput table={"participant"} field={"data.form_id"} value={search.state.form_id || ""} type={"hidden"} />
                </div>
              </div>
            </Suspense>
            <ParticipantFields
              fields={overwriteFieldsConfig || fields}
              // fields={response.isCopy ? [] : overwriteFieldsConfig || fields}
              // readOnly={response.isCopy}
              defaultValue={participant}
              context={fieldsContext}
            />
          </div>
          {selectedActivitiesWithSelectableAddons.length > 0 && (
            <section className="space-y-2">
              <div className="app-container space-y-2">
                <h3 className="text-xl font-bold text-slate-600">
                  <Trans>Select add-ons</Trans>
                </h3>
                {!!booking?.invoice_id ? (
                  <div className="bg-orange-200 text-slate-800 p-3 rounded-md">
                    Add-ons are locked because the booking invoice has already been generated. If you'd like to add add-ons to your booking,
                    please contact us for assistance.
                  </div>
                ) : (
                  <p className="text-slate-500">
                    <Trans>If you require any additional addons, select the amount below</Trans>
                  </p>
                )}
              </div>
              {selectedActivitiesWithSelectableAddons.map((activity) => {
                const product = activity.product;
                const firstPrice = product?.product_prices[0];
                const participation = participant?.participations.find((participation) => participation.sale_item_id === activity.id);
                return (
                  <div key={activity.index} className="app-container">
                    {product && (
                      <div key={activity.index} className="space-y-1">
                        <div className="flex flex-row gap-3 justify-between">
                          <p className="font-bold">{activities[product.activity_slug].name}</p>
                          <p>{formatDuration(activity)}</p>
                        </div>
                        <ProductItem
                          item={{ ...product, product_prices: [] }}
                          locale={establishment?.locale || null}
                          className="rounded-md"
                        />
                      </div>
                    )}
                    <div>
                      <div className="overflow-auto px-0 py-3">
                        <table className="[&_td]:p-1">
                          {activity.addons
                            .filter((addon) => addon.allow_change)
                            .map((addon) => {
                              const fieldIndex = addon.index;
                              const existingSelectedAddon =
                                participation?.sale_item_id === activity.id
                                  ? participation?.addons.find(
                                      (selectedAddon) => !response.isCopy && addon.addon_id === selectedAddon.addon_id,
                                    )
                                  : null;
                              return (
                                <tr key={addon.index}>
                                  <td>
                                    {existingSelectedAddon && (
                                      <RInput
                                        table={"participation_addon"}
                                        field={"id"}
                                        index={fieldIndex}
                                        value={existingSelectedAddon.id}
                                      />
                                    )}
                                    <RInput
                                      table={"participation_addon"}
                                      field={"data.participation_id"}
                                      index={fieldIndex}
                                      type="hidden"
                                      value={tableIdRef("participation", activity.index)}
                                    />
                                    <RInput
                                      table="participation_addon"
                                      field={"data.addon_id"}
                                      index={fieldIndex}
                                      type="hidden"
                                      value={addon.addon_id}
                                    />
                                  </td>
                                  <td>
                                    <div>
                                      <p>{addon.name}</p>
                                      <p className="text-xs">{addonUnits[addon.unit as AddonUnit]?.select_label}</p>
                                    </div>
                                  </td>
                                  <td>
                                    <div className="text-slate-500">
                                      {formatMoney(context, {
                                        nativeAmount: addon.price_amount || 0,
                                        nativeCurrency: booking?.currency_id || firstPrice?.currency_id || defaultCurrencyId,
                                        toCurrency: booking?.currency_id || firstPrice?.currency_id || defaultCurrencyId,
                                        locale: establishment?.locale || defaultLocale,
                                      }) || "-"}
                                    </div>
                                  </td>
                                  <td>
                                    <RSelect
                                      table={"participation_addon"}
                                      field={"data.quantity"}
                                      index={fieldIndex}
                                      className="input w-14"
                                      placeholder="Nr"
                                      defaultValue={existingSelectedAddon?.quantity || "0"}
                                      disabled={!!booking?.invoice_id}
                                    >
                                      <option value="0">0</option>
                                      {arrayFrom(20).map((nr) => (
                                        <option key={nr} value={nr}>
                                          {nr}
                                        </option>
                                      ))}
                                    </RSelect>
                                  </td>
                                </tr>
                              );
                            })}
                        </table>
                      </div>
                    </div>
                  </div>
                );
              })}
            </section>
          )}
          <div className="app-container space-y-6">
            {waivers.map((waiver) => {
              const existingValidParticipantWaiver = participant?.participant_waivers?.find(
                (item) => item.waiver_id === waiver?.id && item.valid,
              );
              const establishmentName = establishment ? getEstablishmentName(establishment) : "Establishment";
              const customError = (el?: HTMLInputElement) => {
                if (!el) return;
                if (el.validity.valueMissing) {
                  el.setCustomValidity(t(lingui.i18n)`Please check this box to continue.`);
                } else {
                  el.setCustomValidity("");
                }
              };
              const enabledCheckbox = !existingValidParticipantWaiver;
              return (
                <div key={waiver.id + !!existingValidParticipantWaiver}>
                  <details>
                    <summary className="link">
                      <Trans>Click to read</Trans> {waiver.translation?.name}
                    </summary>
                    <div className="p-2 border rounded-md border-slate-400">
                      <MarkdocComp
                        content={waiver.translation?.markdoc || ""}
                        comps={baseMarkdocComps}
                        vars={{ operator: establishmentName, participant: { first_name: "you", last_name: "" } }}
                      />
                    </div>
                  </details>
                  {enabledCheckbox && (
                    <Fragment>
                      <RInput table={"participant_waiver"} field={"data.waiver_id"} index={waiver.id} value={waiver.id} type={"hidden"} />
                      <RInput
                        table={"participant_waiver"}
                        field={"data.participant_id"}
                        index={waiver.id}
                        value={tableIdRef("participant")}
                        type={"hidden"}
                      />
                      <OperationInput
                        table={"participant_waiver"}
                        index={waiver.id}
                        value={existingValidParticipantWaiver ? "delete" : "ignore"}
                      />
                    </Fragment>
                  )}
                  <div className="flex flex-row w-fit gap-2 items-center">
                    <label className="flex flex-row w-fit gap-2 items-center">
                      <OperationInput
                        table={"participant_waiver"}
                        value={existingValidParticipantWaiver ? "ignore" : "insert"}
                        index={waiver.id}
                        onInvalid={(e) => customError(e.currentTarget)}
                        onInput={(e) => customError(e.currentTarget)}
                        required={!isManager || search.state.customer_view}
                        disabled={!enabledCheckbox}
                        type={"checkbox"}
                        defaultChecked={!!existingValidParticipantWaiver}
                        className="checkbox peer"
                      />
                      <span className="peer-disabled:opacity-60">
                        <Trans>
                          I have read and agree with the above <span className="font-semibold">{waiver.translation?.name}</span>
                        </Trans>
                      </span>
                      {existingValidParticipantWaiver && (
                        <span>Agreed on {existingValidParticipantWaiver.participant_waivers?.[0]?.created_at_formatted}</span>
                      )}
                    </label>
                    {!!waiver.description && (
                      <Tooltip description={waiver.description}>
                        <DefaultInfoIcon />
                      </Tooltip>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="app-container bg-white py-2">
            <SubmitButton
              className="btn btn-primary p-4 px-10 text-xl"
              onClick={(e) => {
                const form = e.currentTarget?.form;
                if (!/iPad|iPhone|iPod/.test(navigator.userAgent)) return;
                if (!form) return;

                const enableManualScrollTo = false;
                if (!enableManualScrollTo) return;

                const formData = new FormData(form);

                const inputElements = form.getElementsByTagName("input");
                const textareaElements = form.getElementsByTagName("textarea");
                const selectElements = form.getElementsByTagName("select");

                // create solution for iphone to scroll to required input if not filled because
                for (let inputElement of [...inputElements, ...textareaElements, ...selectElements]) {
                  console.log(inputElement.name, inputElement.required, inputElement.value);
                  // get value from formdata because input element might be radio and required and have value and be valid because other radio does have a value.
                  const value = formData.get(inputElement.name);
                  if (inputElement.required && !value) {
                    inputElement.scrollIntoView({ behavior: "smooth", block: "center" });
                    inputElement.focus();
                    return;
                  }
                }
              }}
            >
              {participant ? <Trans>Save</Trans> : <Trans>Register</Trans>}
            </SubmitButton>
          </div>
        </div>
      </ActionForm>
    </Fragment>
  );
}
