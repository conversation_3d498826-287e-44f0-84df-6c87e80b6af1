export const languages = [
  { code: "aa", name: "Afar" },
  { code: "ab", name: "Abkhazian" },
  { code: "ae", name: "Avestan" },
  { code: "af", name: "Afrikaans" },
  { code: "ak", name: "<PERSON><PERSON>" },
  { code: "am", name: "Amharic" },
  { code: "an", name: "Aragonese" },
  { code: "ar", name: "Arabic" },
  { code: "as", name: "Assamese" },
  { code: "av", name: "Avaric" },
  { code: "ay", name: "<PERSON><PERSON><PERSON>" },
  { code: "az", name: "Azerbaijani" },
  { code: "ba", name: "<PERSON>sh<PERSON><PERSON>" },
  { code: "be", name: "Belarusian" },
  { code: "bg", name: "Bulgarian" },
  { code: "bh", name: "Bihari languages" },
  { code: "bi", name: "Bislama" },
  { code: "bm", name: "<PERSON><PERSON><PERSON>" },
  { code: "bn", name: "Bengali" },
  { code: "bo", name: "Tibetan" },
  { code: "br", name: "Breton" },
  { code: "bs", name: "Bosnian" },
  { code: "ca", name: "Catalan; Valencian" },
  { code: "ce", name: "Chechen" },
  { code: "ch", name: "Chamorro" },
  { code: "co", name: "Corsican" },
  { code: "cr", name: "Cree" },
  { code: "cs", name: "Czech" },
  // {
  //   code: "cu",
  //   name: "Church Slavic; Old Slavonic; Church Slavonic; Old Bulgarian; Old Church Slavonic",
  // },
  { code: "cv", name: "Chuvash" },
  { code: "cy", name: "Welsh" },
  { code: "da", name: "Danish" },
  { code: "de", name: "German" },
  { code: "dv", name: "Divehi; Dhivehi; Maldivian" },
  { code: "dz", name: "Dzongkha" },
  { code: "ee", name: "Ewe" },
  { code: "el", name: "Greek" },
  { code: "en", name: "English" },
  { code: "eo", name: "Esperanto" },
  { code: "es", name: "Spanish" },
  { code: "et", name: "Estonian" },
  { code: "eu", name: "Basque" },
  { code: "fa", name: "Persian" },
  { code: "ff", name: "Fulah" },
  { code: "fi", name: "Finnish" },
  { code: "fj", name: "Fijian" },
  { code: "fo", name: "Faroese" },
  { code: "fr", name: "French" },
  { code: "fy", name: "Western Frisian" },
  { code: "ga", name: "Irish" },
  { code: "gd", name: "Gaelic; Scomttish Gaelic" },
  { code: "gl", name: "Galician" },
  { code: "gn", name: "Guarani" },
  { code: "gu", name: "Gujarati" },
  { code: "gv", name: "Manx" },
  { code: "ha", name: "Hausa" },
  { code: "he", name: "Hebrew" },
  { code: "hi", name: "Hindi" },
  { code: "ho", name: "Hiri Motu" },
  { code: "hr", name: "Croatian" },
  { code: "ht", name: "Haitian; Haitian Creole" },
  { code: "hu", name: "Hungarian" },
  { code: "hy", name: "Armenian" },
  { code: "hz", name: "Herero" },
  // {
  //   code: "ia",
  //   name: "Interlingua (International Auxiliary Language Association)",
  // },
  { code: "id", name: "Indonesian" },
  { code: "ie", name: "Interlingue; Occidental" },
  { code: "ig", name: "Igbo" },
  { code: "ii", name: "Sichuan Yi; Nuosu" },
  { code: "ik", name: "Inupiaq" },
  { code: "io", name: "Ido" },
  { code: "is", name: "Icelandic" },
  { code: "it", name: "Italian" },
  { code: "iu", name: "Inuktitut" },
  { code: "ja", name: "Japanese" },
  { code: "jv", name: "Javanese" },
  { code: "ka", name: "Georgian" },
  { code: "kg", name: "Kongo" },
  { code: "ki", name: "Kikuyu; Gikuyu" },
  { code: "kj", name: "Kuanyama; Kwanyama" },
  { code: "kk", name: "Kazakh" },
  { code: "kl", name: "Kalaallisut; Greenlandic" },
  { code: "km", name: "Central Khmer" },
  { code: "kn", name: "Kannada" },
  { code: "ko", name: "Korean" },
  { code: "kr", name: "Kanuri" },
  { code: "ks", name: "Kashmiri" },
  { code: "ku", name: "Kurdish" },
  { code: "kv", name: "Komi" },
  { code: "kw", name: "Cornish" },
  { code: "ky", name: "Kirghiz; Kyrgyz" },
  { code: "la", name: "Latin" },
  { code: "lb", name: "Luxembourgish; Letzeburgesch" },
  { code: "lg", name: "Ganda" },
  { code: "li", name: "Limburgan; Limburger; Limburgish" },
  { code: "ln", name: "Lingala" },
  { code: "lo", name: "Lao" },
  { code: "lt", name: "Lithuanian" },
  { code: "lu", name: "Luba-Katanga" },
  { code: "lv", name: "Latvian" },
  { code: "mg", name: "Malagasy" },
  { code: "mh", name: "Marshallese" },
  { code: "mi", name: "Maori" },
  { code: "mk", name: "Macedonian" },
  { code: "ml", name: "Malayalam" },
  { code: "mn", name: "Mongolian" },
  { code: "mr", name: "Marathi" },
  { code: "ms", name: "Malay" },
  { code: "mt", name: "Maltese" },
  { code: "my", name: "Burmese" },
  { code: "na", name: "Nauru" },
  // {
  //   code: "nb",
  //   name: "Bokmål, Norwegian; Norwegian Bokmål",
  // },
  { code: "nd", name: "Ndebele, North; North Ndebele" },
  { code: "ne", name: "Nepali" },
  { code: "ng", name: "Ndonga" },
  { code: "nl", name: "Dutch" }, // { code: "nl", name: "Dutch; Flemish" },
  // { code: "nn", name: "Norwegian Nynorsk; Nynorsk, Norwegian" },
  { code: "no", name: "Norwegian" },
  { code: "nr", name: "Ndebele, South; South Ndebele" },
  { code: "nv", name: "Navajo; Navaho" },
  { code: "ny", name: "Chichewa; Chewa; Nyanja" },
  { code: "oc", name: "Occitan (post 1500)" },
  { code: "oj", name: "Ojibwa" },
  { code: "om", name: "Oromo" },
  { code: "or", name: "Oriya" },
  { code: "os", name: "Ossetian; Ossetic" },
  { code: "pa", name: "Panjabi; Punjabi" },
  { code: "pi", name: "Pali" },
  { code: "pl", name: "Polish" },
  { code: "ps", name: "Pushto; Pashto" },
  { code: "pt", name: "Portuguese" },
  { code: "qu", name: "Quechua" },
  { code: "rm", name: "Romansh" },
  { code: "rn", name: "Rundi" },
  { code: "ro", name: "Romanian; Moldavian; Moldovan" },
  { code: "ru", name: "Russian" },
  { code: "rw", name: "Kinyarwanda" },
  { code: "sa", name: "Sanskrit" },
  { code: "sc", name: "Sardinian" },
  { code: "sd", name: "Sindhi" },
  { code: "se", name: "Northern Sami" },
  { code: "sg", name: "Sango" },
  { code: "si", name: "Sinhala; Sinhalese" },
  { code: "sk", name: "Slovak" },
  { code: "sl", name: "Slovenian" },
  { code: "sm", name: "Samoan" },
  { code: "sn", name: "Shona" },
  { code: "so", name: "Somali" },
  { code: "sq", name: "Albanian" },
  { code: "sr", name: "Serbian" },
  { code: "ss", name: "Swati" },
  { code: "st", name: "Sotho, Southern" },
  { code: "su", name: "Sundanese" },
  { code: "sv", name: "Swedish" },
  { code: "sw", name: "Swahili" },
  { code: "ta", name: "Tamil" },
  { code: "te", name: "Telugu" },
  { code: "tg", name: "Tajik" },
  { code: "th", name: "Thai" },
  { code: "ti", name: "Tigrinya" },
  { code: "tk", name: "Turkmen" },
  { code: "tl", name: "Tagalog" },
  { code: "tn", name: "Tswana" },
  { code: "to", name: "Tonga (Tonga Islands)" },
  { code: "tr", name: "Turkish" },
  { code: "ts", name: "Tsonga" },
  { code: "tt", name: "Tatar" },
  { code: "tw", name: "Twi" },
  { code: "ty", name: "Tahitian" },
  { code: "ug", name: "Uighur; Uyghur" },
  { code: "uk", name: "Ukrainian" },
  { code: "ur", name: "Urdu" },
  { code: "uz", name: "Uzbek" },
  { code: "ve", name: "Venda" },
  { code: "vi", name: "Vietnamese" },
  { code: "vo", name: "Volapük" },
  { code: "wa", name: "Walloon" },
  { code: "wo", name: "Wolof" },
  { code: "xh", name: "Xhosa" },
  { code: "yi", name: "Yiddish" },
  { code: "yo", name: "Yoruba" },
  { code: "za", name: "Zhuang; Chuang" },
  { code: "zh", name: "Chinese" },
  { code: "zu", name: "Zulu" },
] as const;

export type Language = (typeof languages)[number];

export const languagesCodes = languages.map((language) => language.code);

export type LanguageCode = (typeof languagesCodes)[number];

export const languagesAsIdNames = languages.map((language) => ({ id: language.code, name: language.name }));

export const activeLanguages = ["en", "fr", "id", "ja", "ko", "zh", "ru", "pl", "es"] satisfies LanguageCode[];
// export const activeLanguages = ["en", "fr", "id", "ja", "ko", "zh", "ru", "pl", "es", 'de', 'it', 'nl', 'el'] satisfies LanguageCode[];

export const getLanguage = (languageCode?: LanguageCode) => languages.find((language) => language.code === languageCode);

interface Locale {
  code: string;
  language: string;
  region: string;
  description: string;
}

export const locales = [
  {
    code: "ar-SA",
    language: "Arabic",
    region: "Saudi Arabia",
    description: "Arabic (Saudi Arabia)",
  } as const,
  {
    code: "bn-BD",
    language: "Bangla",
    region: "Bangladesh",
    description: "Bangla (Bangladesh)",
  } as const,
  {
    code: "bn-IN",
    language: "Bangla",
    region: "India",
    description: "Bangla (India)",
  } as const,
  {
    code: "cs-CZ",
    language: "Czech",
    region: "Czech Republic",
    description: "Czech (Czech Republic)",
  } as const,
  {
    code: "da-DK",
    language: "Danish",
    region: "Denmark",
    description: "Danish (Denmark)",
  } as const,
  {
    code: "de-AT",
    language: "German",
    region: "Austria",
    description: "Austrian German",
  } as const,
  {
    code: "de-CH",
    language: "German",
    region: "Switzerland",
    description: "Swiss German",
  } as const,
  {
    code: "de-DE",
    language: "German",
    region: "Germany",
    description: "Standard German (as spoken in Germany)",
  } as const,
  {
    code: "el-GR",
    language: "Greek",
    region: "Greece",
    description: "Modern Greek",
  } as const,
  {
    code: "en-AU",
    language: "English",
    region: "Australia",
    description: "Australian English",
  } as const,
  {
    code: "en-CA",
    language: "English",
    region: "Canada",
    description: "Canadian English",
  } as const,
  {
    code: "en-GB",
    language: "English",
    region: "United Kingdom",
    description: "British English",
  } as const,
  {
    code: "en-IE",
    language: "English",
    region: "Ireland",
    description: "Irish English",
  } as const,
  {
    code: "en-IN",
    language: "English",
    region: "India",
    description: "Indian English",
  } as const,
  {
    code: "en-NZ",
    language: "English",
    region: "New Zealand",
    description: "New Zealand English",
  } as const,
  {
    code: "en-US",
    language: "English",
    region: "United States",
    description: "US English",
  } as const,
  {
    code: "en-ZA",
    language: "English",
    region: "South Africa",
    description: "English (South Africa)",
  } as const,
  {
    code: "es-AR",
    language: "Spanish",
    region: "Argentina",
    description: "Argentine Spanish",
  } as const,
  {
    code: "es-CL",
    language: "Spanish",
    region: "Chile",
    description: "Chilean Spanish",
  } as const,
  {
    code: "es-CO",
    language: "Spanish",
    region: "Columbia",
    description: "Colombian Spanish",
  } as const,
  {
    code: "es-ES",
    language: "Spanish",
    region: "Spain",
    description: "Castilian Spanish (as spoken in Central-Northern Spain)",
  } as const,
  {
    code: "es-MX",
    language: "Spanish",
    region: "Mexico",
    description: "Mexican Spanish",
  } as const,
  {
    code: "es-US",
    language: "Spanish",
    region: "United States",
    description: "American Spanish",
  } as const,
  {
    code: "fi-FI",
    language: "Finnish",
    region: "Finland",
    description: "Finnish (Finland)",
  } as const,
  {
    code: "fr-BE",
    language: "French",
    region: "Belgium",
    description: "Belgian French",
  } as const,
  {
    code: "fr-CA",
    language: "French",
    region: "Canada",
    description: "Canadian French",
  } as const,
  {
    code: "fr-CH",
    language: "French",
    region: "Switzerland",
    description: "Swiss French",
  } as const,
  {
    code: "fr-FR",
    language: "French",
    region: "France",
    description: "Standard French (especially in France)",
  } as const,
  {
    code: "he-IL",
    language: "Hebrew",
    region: "Israel",
    description: "Hebrew (Israel)",
  } as const,
  {
    code: "hi-IN",
    language: "Hindi",
    region: "India",
    description: "Hindi (India)",
  } as const,
  {
    code: "hu-HU",
    language: "Hungarian",
    region: "Hungary",
    description: "Hungarian (Hungary)",
  } as const,
  {
    code: "id-ID",
    language: "Indonesian",
    region: "Indonesia",
    description: "Indonesian (Indonesia)",
  } as const,
  {
    code: "it-CH",
    language: "Italian",
    region: "Switzerland",
    description: "Swiss Italian",
  } as const,
  {
    code: "it-IT",
    language: "Italian",
    region: "Italy",
    description: "Standard Italian (as spoken in Italy)",
  } as const,
  {
    code: "ja-JP",
    language: "Japanese",
    region: "Japan",
    description: "Japanese (Japan)",
  } as const,
  {
    code: "ko-KR",
    language: "Korean",
    region: "Republic of Korea",
    description: "Korean (Republic of Korea)",
  } as const,
  {
    code: "nl-BE",
    language: "Dutch",
    region: "Belgium",
    description: "Belgian Dutch",
  } as const,
  {
    code: "nl-NL",
    language: "Dutch",
    region: "The Netherlands",
    description: "Standard Dutch (as spoken in The Netherlands)",
  } as const,
  {
    code: "no-NO",
    language: "Norwegian",
    region: "Norway",
    description: "Norwegian (Norway)",
  } as const,
  {
    code: "pl-PL",
    language: "Polish",
    region: "Poland",
    description: "Polish (Poland)",
  } as const,
  {
    code: "pt-BR",
    language: "Portugese",
    region: "Brazil",
    description: "Brazilian Portuguese",
  } as const,
  {
    code: "pt-PT",
    language: "Portugese",
    region: "Portugal",
    description: "European Portuguese (as written and spoken in Portugal)",
  } as const,
  {
    code: "ro-RO",
    language: "Romanian",
    region: "Romania",
    description: "Romanian (Romania)",
  } as const,
  {
    code: "ru-RU",
    language: "Russian",
    region: "Russian Federation",
    description: "Russian (Russian Federation)",
  } as const,
  {
    code: "sk-SK",
    language: "Slovak",
    region: "Slovakia",
    description: "Slovak (Slovakia)",
  } as const,
  {
    code: "sv-SE",
    language: "Swedish",
    region: "Sweden",
    description: "Swedish (Sweden)",
  } as const,
  {
    code: "ta-IN",
    language: "Tamil",
    region: "India",
    description: "Indian Tamil",
  } as const,
  {
    code: "ta-LK",
    language: "Tamil",
    region: "Sri Lanka",
    description: "Sri Lankan Tamil",
  } as const,
  {
    code: "th-TH",
    language: "Thai",
    region: "Thailand",
    description: "Thai (Thailand)",
  } as const,
  {
    code: "tr-TR",
    language: "Turkish",
    region: "Turkey",
    description: "Turkish (Turkey)",
  } as const,
  {
    code: "zh-CN",
    language: "Chinese",
    region: "China",
    description: "Mainland China, simplified characters",
  } as const,
  {
    code: "zh-HK",
    language: "Chinese",
    region: "Hond Kong",
    description: "Hong Kong, traditional characters",
  } as const,
  {
    code: "zh-TW",
    language: "Chinese",
    region: "Taiwan",
    description: "Taiwan, traditional characters",
  } as const,
] satisfies Locale[];

export type LocaleCode = (typeof locales)[number]["code"];
