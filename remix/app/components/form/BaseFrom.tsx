import { FormProps, SubmitOptions, useSubmit } from "@remix-run/react";
import { createContext, ReactNode, useContext, useEffect, useId, useRef, useState } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { IdentifierInput, ResponseIdentifierInput } from "~/components/form/DefaultInput";
import autoAnimate from "@formkit/auto-animate";
import { ping } from "~/domain/event/event-fetcher";
import { useIsSuccess } from "~/components/base/Button";
import { confirmMessageKey, fixedResponseIdentifierValue } from "~/misc/vars";
import { removeObjectKeys } from "~/misc/helpers";
import { twMerge } from "tailwind-merge";
import { useIsInterative } from "~/hooks/hooks";
import { useGlobalForm } from "~/components/form/form-hooks";
import { toast } from "~/misc/toast";
import { StateInputKey } from "~/misc/parsers/global-state-parsers";

const ResponseIdContext = createContext(fixedResponseIdentifierValue);

export const useResponseId = () => useContext(ResponseIdContext);

export const ResponseIdProvider = (props: { children: ReactNode }) => {
  const responseId = useId();
  return <ResponseIdContext.Provider value={responseId}>{props.children}</ResponseIdContext.Provider>;
};

const FormIdContext = createContext<{
  id: string;
  formdata: FormData | null;
  isEqual?: boolean;
  isTouched?: boolean;
  isPingPending: boolean;
  successId: string;
}>({
  id: "",
  formdata: null,
  isPingPending: false,
  successId: "",
  // isEqual: "disabled",
});

export const getStr = (formData: FormData | null, key: string) => {
  const value = formData?.get(key);
  return typeof value === "string" ? value : null;
};

export const useFormCtx = () => useContext(FormIdContext);

interface Props extends Omit<FormProps, "ref"> {
  identifier?: string;
  generateIdentifier?: boolean;
  withPing?: boolean;
  confirmMessage?: string;
  autoAnimate?: boolean;
  onCheckEqual?: (args: { initialFormData: FormData; finalFormData: FormData }) => boolean;
}

const defaultFormIdentiefier = "main";

export const defaultEqualCheck = (args: { initialFormData: FormData; finalFormData: FormData }) => {
  const toArrayStr = (formdata: FormData | null) => {
    if (!formdata) return null;
    // const newFormData = copyFormdata(formdata);
    // const excludeKeys = ["response_form_id", "response_identifier", "response_error"] satisfies StateInputKey[];
    // excludeKeys.forEach((key) => {
    //   newFormData.delete(key);
    // });
    // const str = Array.from(newFormData.entries()).toString();
    const str = Array.from(formdata.entries()).toString();
    console.log("str", str);
    return str;
  };
  return toArrayStr(args.initialFormData) === toArrayStr(args.finalFormData);
};

export const ActionForm = (props: Props) => {
  const isInteractive = useIsInterative();
  const [successCount, setSuccessCount] = useState(0);
  const [isValidated, setIsValidated] = useState(false);
  const [isTouched, setIsToched] = useState<undefined | boolean>();
  const [initialFormData, setInitialFormData] = useState<FormData | null>(null);
  const [finalFormData, setFinalFormData] = useState<FormData | null>(null);
  const [isPingPending, setIsPingPending] = useState(false);
  const search = useSearchParams2();
  const refreshFormData = useGlobalForm((state) => state.refresh_formdata);
  const submit = useSubmit();
  const responseId = useResponseId();
  const generatedFormId = useId();
  const successId = generatedFormId + successCount;
  const finalFormId = props.identifier || (props.generateIdentifier ? generatedFormId : defaultFormIdentiefier);
  const formRef = useRef<HTMLFormElement>(null);
  const isSuccess = useIsSuccess(finalFormId);

  useEffect(() => {
    if (search.state.form_success_id === successId) {
      setSuccessCount(successCount + 1);
    }
  }, [search.state.form_success_id]);

  useEffect(() => {
    if (props.autoAnimate !== false) {
      formRef.current && autoAnimate(formRef.current);
    }
  }, []);

  useEffect(() => {
    if (formRef.current && isInteractive) {
      setIsToched(false);
      setInitialFormData(new FormData(formRef.current));
      setFinalFormData(new FormData(formRef.current));
    }
  }, [isInteractive]);

  useEffect(() => {
    if (formRef.current) {
      setIsToched(true);
      setFinalFormData(new FormData(formRef.current));
    }
  }, [search.state.refresh_formdata, refreshFormData]);

  const isEqual = props.onCheckEqual && !!initialFormData && !!finalFormData && props.onCheckEqual({ initialFormData, finalFormData });

  console.log("isequaul", isEqual);
  const newParams = new URLSearchParams(search.params);
  newParams.set("index", "");

  const submitOptions = {
    method: props.method || "POST",
    relative: props.relative || "path",
    action: props.action ?? "?" + newParams.toString(),
    replace: props.replace ?? ((true + "") as any),
    preventScrollReset: props.preventScrollReset ?? true,
  } satisfies SubmitOptions;

  const isGetMethod = submitOptions.method.toLowerCase() === "get";

  return (
    <FormIdContext.Provider
      value={{
        id: finalFormId,
        formdata: finalFormData,
        isEqual: isEqual,
        isTouched: isTouched,
        isPingPending: isPingPending,
        successId: successId,
      }}
    >
      <form
        data-success={isSuccess}
        data-equal={isEqual}
        method={submitOptions.method}
        action={submitOptions.action}
        onInvalid={(e) => {
          console.log("invalid");
          setIsValidated(true);
          // Handle form validation and scrolling to invalid inputs (iOS-compatible)
          const form = e.currentTarget;
          const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
          if (isIOS) {
            const firstInvalidItem = form.querySelectorAll(":invalid")[0];
            if (firstInvalidItem) {
              firstInvalidItem.scrollIntoView({ behavior: "smooth", block: "center" });
            }
          }
        }}
        onChange={() => {
          if (formRef.current) {
            setIsToched(true);
            setFinalFormData(new FormData(formRef.current));
          }
        }}
        onSubmit={async (e) => {
          props.onSubmit?.(e);
          if (e.defaultPrevented) return;
          e.preventDefault();
          const formData = new FormData(e.currentTarget);
          const confirmMessage = formData.get(confirmMessageKey) || props.confirmMessage;
          if (typeof confirmMessage === "string" && confirmMessage) {
            if (!window.confirm(confirmMessage)) {
              e.preventDefault();
              return;
            }
          }
          if (props.withPing || true) {
            setIsPingPending(true);
            const pingSuccess = await ping();
            setIsPingPending(false);
            if (!pingSuccess) {
              window.alert("Unable to submit form - please check your internet connection and try again");
              // toast("No internet connection", "error");
              e.preventDefault();
              return;
              // const promptResult = window.prompt(
              //   `It looks like you don't have an internet connection. If you still want to continue type "yes"`,
              // );
              // if (promptResult?.toLowerCase() !== "yes") {
              //   e.preventDefault();
              //   return;
              // }
            }
          }
          let submitter = (e as unknown as any).nativeEvent.submitter as any | null;
          submit(submitter || e.currentTarget, submitOptions);
        }}
        {...removeObjectKeys(
          props,
          "withPing",
          "onSubmit",
          "method",
          "action",
          "preventScrollReset",
          "confirmMessage",
          "replace",
          "onInvalid",
          "onInput",
          "onChange",
        )}
        className={twMerge(isValidated && "validated", props.className)}
        ref={formRef}
      >
        {props.children}
        {/*should be after children, so the response identifier can be overwritten by children, in case of a action alert */}
        {/*{props.confirmMessage && <ConfirmInput message={props.confirmMessage} />}*/}

        <input type="hidden" name={"form_success_id" satisfies StateInputKey} value={successId} />
        {!isGetMethod && <IdentifierInput value={finalFormId} />}
        {!isGetMethod && <ResponseIdentifierInput value={responseId} />}
      </form>
    </FormIdContext.Provider>
  );
};

// ActionForm.displayName = "ActionForm";
