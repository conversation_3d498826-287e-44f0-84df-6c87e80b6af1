msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: de\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-22 16:56\n"
"Last-Translator: \n"
"Language-Team: German\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: de\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr ""

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr ""

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr ""

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:837
msgid "Booking"
msgstr ""

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:993
msgid "Certification"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1407
msgid "Click to read"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Copy"
msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1005
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:997
msgid "Dives"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Edit"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1195
msgid "Email OTP"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr ""

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr ""

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr ""

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr ""

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr ""

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr ""

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr ""

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr ""

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr ""

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1449
msgid "I have read and agree with the above <0>{0}</0>"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "If you require any additional addons, select the amount below"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr ""

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr ""

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr ""

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1001
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr ""

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr ""

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr ""

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr ""

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr ""

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr ""

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr ""

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr ""

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1009
msgid "Phone"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr ""

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1397
msgid "Please check this box to continue."
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:877
msgid "Please select the activity (or activities) you're attending."
msgstr ""

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:824
msgid "Preferred language"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1241
msgid "Recipient email"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1143
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Register"
msgstr ""

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr ""

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1185
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:989
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Save"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1272
msgid "Select add-ons"
msgstr ""

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:210
msgid "select country"
msgstr ""

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:240
msgid "Select country"
msgstr ""

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
#: app/components/CountrySelect.tsx:179
msgid "Select Country"
msgstr ""

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr ""

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr ""

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr ""

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr ""

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr ""

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr ""

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr ""

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr ""

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr ""

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr ""

