msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: es\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-22 16:47\n"
"Last-Translator: \n"
"Language-Team: Spanish\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: es-ES\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Dirección"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agencia"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Aprobado – No encuentro condiciones que considere incompatibles con el buceo recreativo o el buceo libre."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "El código de autenticación ha expirado"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "BCD"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Fecha de nacimiento"

#: app/routes/_all._catch.participant_.mutate.tsx:837
msgid "Booking"
msgstr "Reserva"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Traer mi propio equipo"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Detalles del certificado"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Nivel del certificado"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:993
msgid "Certification"
msgstr "Certificación"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Organización de certificación"

#: app/routes/_all._catch.participant_.mutate.tsx:1407
msgid "Click to read"
msgstr "Haz clic para leer"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Clínica/Hospital"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Títulos/Credenciales Clínicas"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Compañía y número de póliza"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Copy"
msgstr "Copiar"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1005
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "País"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Creado por el <0>Comité de Evaluación Médica</0> para Buceadores en asociación con los siguientes organismos:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Fecha (dd/mm/aaaa)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Fecha de nacimiento"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Detalles específicos de la inmersión"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Médico del Buceador"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:997
msgid "Dives"
msgstr "Inmersiones"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "¿Tiene un seguro que cubra las actividades subacuáticas? Por favor, complete sus datos a continuación"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "p.ej. amigo, padre o madre"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "p.ej. Nombre del hotel"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "p.ej. alergia a los frutos secos"

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Edit"
msgstr "Editar"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "Correo electrónico"

#: app/routes/_all._catch.participant_.mutate.tsx:1195
msgid "Email OTP"
msgstr "OTP de correo electrónico"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Datos de contacto de emergencia"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Nombre del contacto de emergencia"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Número de teléfono del contacto de emergencia"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Introducir una ubicación"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Introducir número"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Explora Bali bajo el agua"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Comentarios"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Aletas"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Nombre"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Alergias alimentarias"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Amigo/Amiga"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Sin gluten"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Maps"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Altura"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "La dirección de correo electrónico ya está en uso por otra cuenta."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Dirección de domicilio/residencia"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Hotel"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1449
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "He leído y estoy de acuerdo con lo anterior <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "If you require any additional addons, select the amount below"
msgstr "Si requiere complementos adicionales, seleccione la cantidad a continuación"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "¡Importante! Asegúrese de que el contacto de emergencia no sea alguien que participe en la actividad."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Seguro"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Credenciales no válidas"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Dirección de correo electrónico no válida"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1001
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Última inmersión"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Apellido"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Máscara"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "¿Podemos preguntar cómo se enteró de nosotros?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "¿Podemos contactarlo para preguntarle sobre su experiencia?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Preferencias de comida"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Formulario de Evaluación del Médico Examinador"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Nombre del Médico Examinador"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Hace más de 1 año"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "hace más de 2 años"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "El nombre ya existe en este establecimiento"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Nombre del padre o tutor"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Nombre del participante"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "No"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "No se realizaron cambios"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Sin preferencia"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "No aprobado – Encuentro condiciones que considero incompatibles con el buceo recreativo o el buceo libre"

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Número de inmersiones"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "O una tarjeta de identificación local válida"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "O talla de camiseta si no conoce su talla de traje de neopreno."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Otra información (opcional)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Equipo propio"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "No se encontró la página o no está autorizado"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Detalles del participante"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Nombre del participante"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Pasaporte"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1009
msgid "Phone"
msgstr "Teléfono"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Número de teléfono"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Sello del Médico/Clínica (opcional)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Planificación"

#: app/routes/_all._catch.participant_.mutate.tsx:1397
msgid "Please check this box to continue."
msgstr "Por favor, marque esta casilla para continuar."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Por favor, complete cualquier información importante o preguntas adicionales a continuación"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Por favor, seleccione en caso de que traiga su propio equipo."

#: app/routes/_all._catch.participant_.mutate.tsx:877
msgid "Please select the activity (or activities) you're attending."
msgstr "Por favor, seleccione la actividad (o actividades) a las que asistirá."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:824
msgid "Preferred language"
msgstr "Idioma preferido"

#: app/routes/_all._catch.participant_.mutate.tsx:1241
msgid "Recipient email"
msgstr "Correo electrónico del destinatario"

#: app/routes/_all._catch.participant_.mutate.tsx:1143
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Register"
msgstr "Registrarse"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Regulador"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Relación"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Investigación y reserva más fáciles"

#: app/routes/_all._catch.participant_.mutate.tsx:1185
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "¿Participante recurrente? Recupere su registro mediante la verificación de correo electrónico OTP."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:989
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Número de habitación"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Número de habitación de su estancia"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Save"
msgstr "Guardar"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "Seleccionar"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Seleccionar"

#: app/routes/_all._catch.participant_.mutate.tsx:1272
msgid "Select add-ons"
msgstr "Seleccionar complementos"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:210
msgid "select country"
msgstr "Seleccionar país"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:240
msgid "Select country"
msgstr "Seleccionar país"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
#: app/components/CountrySelect.tsx:179
msgid "Select Country"
msgstr "Seleccionar País"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Seleccionar nivel"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Seleccionar organización"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Seleccionar talla"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Talla de zapato"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Firma del médico certificado u otro proveedor médico legalmente certificado"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Firma del padre o tutor"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Firma del participante"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Estancia"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "La persona mencionada anteriormente solicita su opinión sobre su aptitud médica para participar en el entrenamiento o actividad de buceo recreativo o buceo libre. Por favor, visite <0>uhms.org</0> para obtener orientación médica sobre las condiciones médicas relacionadas con el buceo. Revise las áreas relevantes para su paciente como parte de su evaluación."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Fecha de hoy"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Vegano/Vegana"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Vegetariano/Vegetariana"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "La contraseña debe tener al menos 6 caracteres."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Sin reserva"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Peso"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Cinturón de lastre"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Traje de neopreno"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Talla de traje de neopreno"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "Se prefiere WhatsApp"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "En los últimos 3 meses"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "En los últimos 6 meses"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "En el último año"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Años de experiencia"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Sí"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Tu nombre de usuario de Instagram"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "YouTube"

