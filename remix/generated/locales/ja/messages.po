msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ja\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-22 16:47\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "住所"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "代理店"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "参加することに同意します – レクリエーショナルスクーバダイビングまたはフリーダイビングに適さないと考えられる状態は\n"
"ありませんでした。"

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "認証コードの有効期限が切れています。"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "浮力調整装置"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "生年月日"

#: app/routes/_all._catch.participant_.mutate.tsx:837
msgid "Booking"
msgstr "予約 "

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "自分の持ち込み"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "認定証の詳細"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "証明書レベル"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:993
msgid "Certification"
msgstr "認定"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "認定機関"

#: app/routes/_all._catch.participant_.mutate.tsx:1407
msgid "Click to read"
msgstr "クリックして読む"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "クリニック/病院"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "資格"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "会社とポリシー番号"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Copy"
msgstr "コピー"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1005
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "国/地域"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "作成： <0>Diver Medical Screen Committee</0> 提携・協力団体："

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "日付（日/月/年）"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "生年月日"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "ダイビングの具体的な詳細"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "ダイバーメディカル"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:997
msgid "Dives"
msgstr "ダイビング"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "水中活動をカバーする保険はお持ちですか？以下に詳細を記入してください。"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "例えば、友人、父、または母\""

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "例えば、ホテル名"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "例えば、ナッツアレルギー"

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Edit"
msgstr "編集"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "メール"

#: app/routes/_all._catch.participant_.mutate.tsx:1195
msgid "Email OTP"
msgstr "メール OTP"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "緊急連絡先"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "緊急連絡先の氏名"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "緊急連絡先の電話番号"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "場所を入力してください"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "番号を入力"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "バリの海中を探検する"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "フィードバック"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "フィン"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "名"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "食物アレルギー"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "友達"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "グルテンフリー"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr ""

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "身長"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "そのメールアドレスはすでに別のアカウントで使用されています。"

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "自宅住所"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "ホテル"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1449
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "上記を読みましたし、同意します <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "If you require any additional addons, select the amount below"
msgstr "追加のアドオンが必要な場合は、以下から数量を選択してください"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "重要！緊急連絡先が活動に参加する人でないことを確認してください。"

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "保険"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "無効なログイン"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "無効な e メール アドレス"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1001
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "前回のダイブ"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "姓"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "マスク"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "当社をどのように知りましたか？"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "ご経験についてお伺いしてもよろしいですか？"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "食事の希望 / 食事の好み"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "健康診断医師による評価シート"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "医師名"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "1年以上前"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "2年以上前"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "この施設にはすでに同じ名前が存在しています"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "保護者の方のお名前:"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "参加者の氏名"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "いいえ"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "変更はありませんでした"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "設定なし"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "参加することに同意しません – レクリエーショナルスクーバダイビングあるいはフリーダイビングに適さないと考えられる状\n"
"態がありました。"

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "ダイブの回数"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "または有効な現地の身分証明書。"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "または、ウェットスーツのサイズがわからない場合はTシャツのサイズをご記入ください"

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "その他の情報 (任意)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "持参の器材"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "ページが見つからないか、権限がありません"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "参加者の詳細"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "参加者名"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "パスポート"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1009
msgid "Phone"
msgstr "電話番号"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "電話番号"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "医師/クリニックのスタンプ（任意）"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "計画"

#: app/routes/_all._catch.participant_.mutate.tsx:1397
msgid "Please check this box to continue."
msgstr "続行するには、このボックスをチェックしてください"

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "以下に追加の重要な情報や質問を記入してください。"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "あなた自身のギアを持参する場合に選択してください。"

#: app/routes/_all._catch.participant_.mutate.tsx:877
msgid "Please select the activity (or activities) you're attending."
msgstr "参加するアクティビティ（またはアクティビティ）を選択してください。"

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:824
msgid "Preferred language"
msgstr "優先言語"

#: app/routes/_all._catch.participant_.mutate.tsx:1241
msgid "Recipient email"
msgstr "メールアドレス"

#: app/routes/_all._catch.participant_.mutate.tsx:1143
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Register"
msgstr "登録"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "レギュレーター"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "関係性"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "研究と予約が簡単になりました"

#: app/routes/_all._catch.participant_.mutate.tsx:1185
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "以前にご参加されたことがありますか？OTPメール認証で登録情報を復元してください。"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:989
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "部屋番号"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "滞在先の部屋番号。"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Save"
msgstr "保存"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "選択してください"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "選択してください"

#: app/routes/_all._catch.participant_.mutate.tsx:1272
msgid "Select add-ons"
msgstr "アドオンを選択"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:210
msgid "select country"
msgstr "国を選択"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:240
msgid "Select country"
msgstr "国を選択"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
#: app/components/CountrySelect.tsx:179
msgid "Select Country"
msgstr "国を選択してください"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "レベルを選択してください"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "組織を選択してください"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "サイズを選択"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "シューズのサイズ"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "医師の署名"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "保護者の方のご署名:"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "署名参加者"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "宿泊"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "上記の者は、レクリエーショナルダイビングあるいはフリーダイビングのトレーニングまたは活動に参加することに適しているかど\n"
"うか、先生の意見を求めています。ダイビングに関連する医学的状態に関する医学的ガイドラインについては、 <0>uhms.org</0> をご確認\n"
"ください。評価中に依頼人に関係する（2ページの質問に「はい」と回答した）部分についてご確認ください。"

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "今日の日付"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "ビーガン"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "ベジタリアン"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "パスワードは少なくとも6文字以上でなければなりません"

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "直接来店"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "体重"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "ウェイトベルト"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "ウェットスーツ"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "ウェットスーツのサイズ"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "WhatsAppを優先。"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "過去3ヶ月以内"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "過去6ヶ月以内"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "過去 1 年以内"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "経験年数"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "はい"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "あなたのインスタグラムのハンドル"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr ""

