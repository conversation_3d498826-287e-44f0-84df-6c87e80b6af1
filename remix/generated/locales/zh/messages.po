msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: zh\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-22 16:47\n"
"Last-Translator: \n"
"Language-Team: Chinese Traditional\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: zh-TW\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "地址"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "代理商"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "批准 – 我并未发现任何不适合休闲水肺潜水或自由潜水的症状。"

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "驗證碼已過期"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "浮力調整裝置"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "出生日期"

#: app/routes/_all._catch.participant_.mutate.tsx:837
msgid "Booking"
msgstr "預約"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "自備食物"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "證書詳細資訊"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "證書等級"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:993
msgid "Certification"
msgstr "憑證"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "認證機構"

#: app/routes/_all._catch.participant_.mutate.tsx:1407
msgid "Click to read"
msgstr "點擊閱讀"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "診所/醫院"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "臨床學位/證書"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "公司及保單號碼"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Copy"
msgstr "複製"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1005
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "國家"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "由<0>潛水醫學篩查委員會</0>與以下機構聯合製作:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "日期 （日 / 月 / 年）"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "出生日期"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "潛水具體細節"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "潛水員醫療表格"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:997
msgid "Dives"
msgstr "潛水次數"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "您是否有保險涵蓋水下活動？請在下方填寫您的詳細資料。"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "例如朋友、父親、母親"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "例如，飯店名稱"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "例如：堅果過敏"

#: app/routes/_all._catch.participant_.mutate.tsx:1136
msgid "Edit"
msgstr "編輯"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "电子邮件"

#: app/routes/_all._catch.participant_.mutate.tsx:1195
msgid "Email OTP"
msgstr "電郵驗證碼"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "緊急聯絡資料"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "緊急聯絡資料"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "緊急聯繫人電話："

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "輸入地點"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "輸入號碼"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "探索巴厘島海底"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "問題反饋"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "蛙鞋"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "名"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "食物過敏"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "朋友"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "不含麩質"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr ""

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "高度"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "該電子郵件地址已被另一個帳戶使用。"

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "住宅地址"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "飯店"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1449
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "我已閱讀並同意上述內容 <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "If you require any additional addons, select the amount below"
msgstr "如果您需要任何額外附加功能，請在下面選擇數量"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "重要！確保緊急聯絡人不是參加活動的人。"

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "保險"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "無效憑證"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "不正確電子郵件地址"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1001
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "最後一次潛水"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "姓氏"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "面罩"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "可以問您是從哪裡得知我們的嗎?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "我們可以聯繫您詢問您的經驗嗎？"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "餐點偏好"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "醫學檢查員評估表"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "醫學檢查員姓名"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "超過一年"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "一年多以前"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "該名稱已存在於此機構。"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "父母或監護人姓名"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "參加者姓名"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "否"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "沒有執行變更"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "無偏好"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "未核准 – 我發現一些與休閒水肺潛水或自由潛水不相容的條件。"

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "潛水次數"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "或有效的本地身分證。"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "或者是T恤尺寸，如果您不知道您的濕衣尺寸。"

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "其他資訊 (選填)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "緊急聯絡人姓名"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "未找到頁面或您無權訪問"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "參與者詳情"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "参加者姓名"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "護照"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1009
msgid "Phone"
msgstr "电话"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "电话号码"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "医生 / 医院盖章 ( 可选)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "規劃"

#: app/routes/_all._catch.participant_.mutate.tsx:1397
msgid "Please check this box to continue."
msgstr "請勾選此框繼續"

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "請在下方填寫任何其他重要資訊或問題"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "如您自帶裝備，請選擇"

#: app/routes/_all._catch.participant_.mutate.tsx:877
msgid "Please select the activity (or activities) you're attending."
msgstr "請選擇您參加的活動（或多項活動）。"

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:824
msgid "Preferred language"
msgstr "偏好語言"

#: app/routes/_all._catch.participant_.mutate.tsx:1241
msgid "Recipient email"
msgstr "收件人電子郵件"

#: app/routes/_all._catch.participant_.mutate.tsx:1143
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Register"
msgstr "登錄"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "調壓器"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "關係"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "研究與預訂變得更簡便"

#: app/routes/_all._catch.participant_.mutate.tsx:1185
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "再次參與者？請透過一次性密碼電子郵件驗證找回您的註冊資訊。"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:989
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "房間號碼"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "您入住的房間號。"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1497
msgid "Save"
msgstr "儲存"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "选择"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "选择"

#: app/routes/_all._catch.participant_.mutate.tsx:1272
msgid "Select add-ons"
msgstr "選擇附加功能"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:210
msgid "select country"
msgstr "選擇國家"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:240
msgid "Select country"
msgstr "選擇國家"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
#: app/components/CountrySelect.tsx:179
msgid "Select Country"
msgstr "選擇國家"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "選擇等級"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "選擇組織"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "選擇尺寸"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "鞋碼"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "医师签名"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "父母或監護人簽名"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "簽名參與者"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "住宿"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "以上姓名的人士要求你提供有关是否适合其参加休闲水肺潜水或自由潜水训练或活动的医学意见。 请访问 <0>uhms.org</0> 以获\n"
"取与潜水有关的医学状况的医学指导， 在评估过程中查阅与你的病患有关的部分。"

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "今天的日期"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "素食"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "素食者"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "密碼必須至少6個字符長"

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "走進來"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "重量"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "重量"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "潛水衣"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "密碼必須至少6個字符長 (Mìmǎ bìxū zhìshǎo 6 gè zìfú cháng)"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "優先使用WhatsApp。"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "過去 3 個月內"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "過去 6 個月內"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "過去一年內"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "多年經驗"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "是"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "您的 Instagram 帳號"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr ""

